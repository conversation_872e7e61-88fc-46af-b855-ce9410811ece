<script setup>
import { some } from 'lodash-es';
import { onMounted } from 'vue';

const props = defineProps({
  artifactId: {
    type: String,
    default: '',
  },
  artifactType: {
    type: String,
    default: '',
  },
  agentDetails: {
    type: Object,
    default: () => ({}),
  },
  title: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['close']);

const state = reactive({
  is_loading: false,
  form_data: {},
  all_dashboards: {},
});

const $t = inject('$t');
const $services = inject('$services');

function getIndex(current_dashboard) {
  let increment = 1;
  const report_data = current_dashboard?.report_data || {};
  let unique = (Object.keys(report_data).length || 0) + increment;
  let not_unique = some(report_data, ['i', unique]);
  while (not_unique) {
    increment += 1;
    unique = (Object.keys(report_data).length || 0) + increment;
    not_unique = some(report_data, ['i', unique]);
  }
  return unique;
}

async function onAdd() {
  const current_dashboard = state.all_dashboards.find(dashboard => dashboard.uid === state.form_data.dashboard);
  const current_dashboard_report_data = current_dashboard?.report_data || {};
  const widget = {
    x: 0,
    y: (Object.keys(current_dashboard_report_data).length && Object.keys(current_dashboard_report_data).length * 15)
      || 15,
    w: 12,
    h: 20,
    i: getIndex(current_dashboard),
    source: {
      type: 'naavix',
      service: 'naavix',
    },
    type: `naavix_${props.artifactType}`,
    uid: crypto.randomUUID(),
    data: {
      module: 'naavix',
      properties: {
        source: {
          type: 'naavix',
          service: 'naavix',
          artifact_id: props.artifactId,
        },
      },
      name: state.form_data.title,
      artifact_id: props.artifactId,
      agent_name: props.agentDetails.module,
      resource_id: props.agentDetails.resource_id,
      resource_type: props.agentDetails.resource_type,
      type: `naavix_${props.artifactType}`,
    },
    moved: false,
  };
  current_dashboard.report_data[widget.uid] = widget;
  await $services.dashboard.patch({
    id: state.form_data.dashboard,
    body: current_dashboard,
  });
  emit('close');
}

onMounted(async () => {
  state.is_loading = true;
  state.form_data.title = props.title;
  const { data } = await $services.dashboard.getAll();
  state.all_dashboards = data.report;
  state.is_loading = false;
});
</script>

<template>
  <HawkModalContainer id="naavix-graph-to-dashboard-modal">
    <Vueform
      v-model="state.form_data"
      sync
      size="sm"
      :display-errors="false"
      :display-messages="false"
      :columns="{
        default: {
          container: 12,
          label: 3,
          wrapper: 9,
        },
        sm: {
          label: 4,
        },
        md: {
          label: 4,
        },
        lg: {
          label: 4,
        },
      }"
      :endpoint="onAdd"
    >
      <div class="col-span-12">
        <HawkModalHeader @close="emit('close')">
          <template #title>
            {{ $t('Add to dashboard') }}
          </template>
        </HawkModalHeader>
        <HawkModalContent>
          <SelectElement
            name="dashboard"
            :label="$t('Dashboard')"
            :placeholder="$t('Select dashboard')"
            :items="state.all_dashboards?.map?.(dashboard => ({ name: dashboard.name || 'Dashboard', uid: dashboard.uid }))?.filter?.(dashboard => dashboard.uid)"
            label-prop="name"
            value-prop="uid"
            :native="false"
            :search="true"
            :loading="state.is_loading"
            append-to="#naavix-graph-to-dashboard-modal"
            class="mb-6"
            :rules="['required']"
          />
          <TextElement
            name="title"
            :label="$t('Title')"
            :placeholder="$t('Enter title')"
          />
        </HawkModalContent>
        <HawkModalFooter>
          <template #right>
            <div class="flex justify-end w-full col-span-full">
              <ButtonElement
                name="cancel"
                class="mr-4"
                :secondary="true"
                @click="emit('close')"
              >
                {{ $t('Cancel') }}
              </ButtonElement>
              <ButtonElement
                name="add"
                :submits="true"
              >
                {{ $t('Add') }}
              </ButtonElement>
            </div>
          </template>
        </HawkModalFooter>
      </div>
    </Vueform>
  </HawkModalContainer>
</template>
