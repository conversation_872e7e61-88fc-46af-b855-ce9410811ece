<script setup>
import GalleryClassFilter from '~/terra/molecules/gallery/gallery-filters/gallery-class-filter.vue';
import GalleryDateFilter from '~/terra/molecules/gallery/gallery-filters/gallery-date-filter.vue';
import GalleryTypeFilter from '~/terra/molecules/gallery/gallery-filters/gallery-type-filter.vue';
import GalleryUserFilter from '~/terra/molecules/gallery/gallery-filters/gallery-user-filter.vue';

const props = defineProps({
  isThermViewer: {
    type: <PERSON><PERSON><PERSON>,
    default: false,
  },
  filterState: {
    type: Object,
    default: () => ({}),
  },
  allAttachments: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits(['applyFilters']);

// Computed properties for loading filters
const filter_options = computed(() => {
  const created_by_set = new Set();
  const feature_type_set = new Set();

  props.allAttachments.forEach((attachment) => {
    if (attachment.created_by) {
      created_by_set.add(attachment.created_by);
    }
    if (attachment.feature_type) {
      feature_type_set.add(attachment.feature_type);
    }
  });

  return {
    created_by: Array.from(created_by_set),
    classes: Array.from(feature_type_set),
  };
});

function handleFilterUpdate(filter) {
  emits('applyFilters', filter);
}
</script>

<template>
  <div class="flex items-center gap-x-2 flex-wrap">
    <GalleryTypeFilter
      v-if="isThermViewer"
      :default_value="filterState?.type"
      @apply-filters="handleFilterUpdate($event)"
    />
    <GalleryDateFilter
      :default_value="filterState?.created_at"
      @apply-filters="handleFilterUpdate($event)"
    />
    <GalleryUserFilter
      v-if="filter_options.created_by?.length"
      :default_value="filterState?.created_by"
      :users="filter_options.created_by"
      @apply-filters="handleFilterUpdate($event)"
    />
    <GalleryClassFilter
      v-if="!isThermViewer && filter_options.classes?.length"
      :default_value="filterState?.classes"
      :classes="filter_options.classes"
      @apply-filters="handleFilterUpdate($event)"
    />
  </div>
</template>
