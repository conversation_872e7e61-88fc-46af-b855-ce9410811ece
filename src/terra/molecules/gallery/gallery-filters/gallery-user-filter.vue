<script setup>
import { reactive } from 'vue';

const props = defineProps({
  // eslint-disable-next-line vue/prop-name-casing
  default_value: {
    type: Array,
    default: () => ([]),
  },
  users: {
    type: Array,
    default: () => [],
  },
});
const emits = defineEmits(['applyFilters']);

const state = reactive({
  selected_users_map: {},
});

function initSelectedUsers() {
  state.selected_users_map = (props.default_value?.value || []).reduce((acc, user_uid) => {
    acc[user_uid] = true;
    return acc;
  }, {});
}

function handleClear() {
  state.selected_users_map = {};
}

function handleFilterSelection(close = null) {
  const users = Object.entries(state.selected_users_map).filter(([, value]) => value).map(([key]) => key);
  emits('applyFilters', { uid: 'created_by', value: users });
  if (close) {
    close();
  }
}
</script>

<template>
  <hawk-menu position="fixed" additional_trigger_classes="!ring-0 w-full" @open="initSelectedUsers">
    <template #trigger="{ open }">
      <div class="flex items-center">
        <div class="text-sm">
          {{ $t('Created by') }}
        </div>
        <IconHawkChevronDown
          class="transition-transform"
          :class="{ 'rotate-180 !visible': open }"
        />
      </div>
    </template>
    <template #content="{ close }">
      <div>
        <div class="w-[350px] max-h-[400px] p-2 scrollbar">
          <div v-for="user_uid in users" :key="user_uid">
            <div class="w-full p-1.5 flex items-center hover:bg-gray-50 rounded-lg cursor-pointer">
              <HawkCheckbox
                :model-value="state.selected_users_map[user_uid] ? 'checked' : 'unchecked'"
                size="sm"
                tristate
                @update:model-value="state.selected_users_map[user_uid] = !state.selected_users_map[user_uid]"
              >
                <HawkMembers :members="user_uid" size="xs" type="label" class="mb-1" />
              </HawkCheckbox>
            </div>
          </div>
        </div>
        <div class="flex justify-between px-4 py-1 border-t text-sm font-semibold">
          <div class="text-gray-600 cursor-pointer p-2" @click="handleClear()">
            {{ $t('Clear') }}
          </div>
          <div class="text-primary-700 cursor-pointer p-2" @click="handleFilterSelection(close)">
            {{ $t('Apply') }}
          </div>
        </div>
      </div>
    </template>
  </hawk-menu>
</template>
