<script setup>
import { useTerraStore } from '~/terra/store/terra.store';

const props = defineProps({
  // eslint-disable-next-line vue/prop-name-casing
  default_value: {
    type: Array,
    default: () => ([]),
  },
  classes: {
    type: Array,
    default: () => [],
  },
});
const emits = defineEmits(['applyFilters']);

const terra_store = useTerraStore();

const state = reactive({
  selected_classes_map: {},
});

const class_group_list = computed(() => {
  const class_group_set = new Set();
  props.classes.forEach((class_uid) => {
    const feature_type = terra_store.feature_types_by_uid[class_uid];
    if (feature_type)
      class_group_set.add(feature_type.featureTypeGroup);
  });
  const class_groups = Array.from(class_group_set);
  return class_groups.map((class_group_uid) => {
    const class_group = terra_store.ftg_map[class_group_uid];
    return {
      name: class_group?.name,
      feature_types: (class_group.featureTypes || [])?.filter(ft => props.classes.includes(ft.uid)),
    };
  }).filter(group => group.feature_types.length);
});

function initSelectedClasses() {
  state.selected_classes_map = (props.default_value?.value || []).reduce((acc, class_uid) => {
    acc[class_uid] = true;
    return acc;
  }, {});
}

function getGroupModelValue(class_group) {
  const selected_feature_types = class_group.feature_types.filter(ft => state.selected_classes_map[ft.uid]);
  if (selected_feature_types.length === class_group.feature_types.length)
    return 'checked';
  if (selected_feature_types.length)
    return 'intermediate';
  return 'unchecked';
}

function handleGroupSelection(class_group, value) {
  class_group.feature_types.forEach((feature_type) => {
    state.selected_classes_map[feature_type.uid] = value === 'checked';
  });
}

function handleClear() {
  state.selected_classes_map = {};
}

function handleFilterSelection(close = null) {
  const classes = Object.entries(state.selected_classes_map).filter(([, value]) => value).map(([key]) => key);
  emits('applyFilters', { uid: 'classes', value: classes });
  if (close) {
    close();
  }
}
</script>

<template>
  <hawk-menu position="fixed" additional_trigger_classes="!ring-0 w-full" @open="initSelectedClasses">
    <template #trigger="{ open }">
      <div class="flex items-center">
        <div class="text-sm">
          {{ $t('Class') }}
        </div>
        <IconHawkChevronDown
          class="transition-transform"
          :class="{ 'rotate-180 !visible': open }"
        />
      </div>
    </template>
    <template #content="{ close }">
      <div>
        <div class="w-[350px] max-h-[400px] p-4 scrollbar overflow-x-hidden">
          <div v-for="class_group in class_group_list" :key="class_group.name">
            <HawkCheckbox
              :model-value="getGroupModelValue(class_group)"
              size="sm"
              tristate
              class="my-2"
              @update:model-value="handleGroupSelection(class_group, $event)"
            >
              <span class="text-sm font-medium text-gray-900">
                <HawkText :content="class_group.name" :length="35" />
              </span>
            </HawkCheckbox>
            <div class="ml-2">
              <div v-for="feature_type in class_group.feature_types" :key="feature_type.uid">
                <div class="p-1.5 flex items-center hover:bg-gray-50 rounded-lg cursor-pointer flex-wrap">
                  <HawkCheckbox
                    :model-value="state.selected_classes_map[feature_type.uid] ? 'checked' : 'unchecked'"
                    size="sm"
                    tristate
                    @update:model-value="state.selected_classes_map[feature_type.uid] = !state.selected_classes_map[feature_type.uid]"
                  >
                    <span class="text-sm text-gray-700">
                      <HawkText :content="feature_type.name" :length="35" />
                    </span>
                  </HawkCheckbox>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex justify-between px-4 py-1 border-t text-sm font-semibold">
          <div class="text-gray-600 cursor-pointer p-2" @click="handleClear()">
            {{ $t('Clear') }}
          </div>
          <div class="text-primary-700 cursor-pointer p-2" @click="handleFilterSelection(close)">
            {{ $t('Apply') }}
          </div>
        </div>
      </div>
    </template>
  </hawk-menu>
</template>
