<!-- eslint-disable vue/prop-name-casing -->
<script setup>
import { Dialog, DialogPanel, TransitionChild, TransitionRoot } from '@headlessui/vue';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import { flatten, groupBy, isEqual } from 'lodash-es';
import { watch } from 'vue';
import HawkSidebar from '~/common/components/organisms/hawk-sidebar.vue';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import { sleep } from '~/common/utils/common.utils';
import AttachmentDetails from '~/terra/molecules/gallery/attachment-details.vue';
import GalleryFilters from '~/terra/molecules/gallery/gallery-filters/gallery-filters.vue';
import { useFeatureAttachmentsPdfExporter } from '~/terra/utils/feature-attachments-pdf-exporter';
import { useGalleryView } from '~/terra/utils/gallery-view.composable.js';

const props = defineProps({
  store: {
    type: Object,
  },
  is_therm_viewer: {
    type: Boolean,
    default: false,
  },
  show_select_checkbox: {
    type: Boolean,
    default: false,
  },
});

dayjs.extend(isBetween);
const { $t } = useCommonImports();
const { updateAttachmentPoint, sortAttachments } = useGalleryView();

const state = reactive({
  is_loading: false,
  feature_attachments_map: {},
  all_attachments: [],
  filter: {},
  is_pdf_exporting: false,
  current_export_file_index: 1,
  total_files_to_export: 1,
  is_pdf_export_complete: false,
  select_all_attachments: false,
  selected_attachments_map: {},
  all_available_attachments: [],
});

const selected_attachments = computed(() => {
  return Object.keys(state.selected_attachments_map).filter(uid => state.selected_attachments_map[uid]);
});
const attachment_options = computed(() => {
  const { created_at, type, created_by, classes } = state.filter;
  if (!created_at && !type && !created_by && !classes)
    return {};
  const grouped_data = groupBy(state.all_attachments, attachment => attachment?.feature_uid);
  const feature_attachments_map = Object.entries(grouped_data).reduce((acc, [feature_uid, all_feature_attachments]) => {
    const project_uid = all_feature_attachments[0]?.project_uid;
    acc[feature_uid] = {
      feature_uid,
      project_uid,
      all_feature_attachments,
      attachments_count: all_feature_attachments.length,
    };
    return acc;
  }, {});
  return { attachments_list: Object.values(feature_attachments_map) };
});
const gallery_tools = computed(() => {
  return [
    {
      tooltip: $t('Gallery View'),
      icon: IconHawkGridOne,
      event: 'gallery-view',
      action: async () => {
        if (!props.store.gallery_view_state?.is_active) {
          state.is_loading = true;
          props.store.set_gallery_state('is_active', true);
          await updateAttachmentPoint(props.store, attachment_options.value);
          getData();
          state.is_loading = false;
        }
        else {
          await closeSidebar();
        }
      },
    },
  ];
});
const available_attachments_map = computed(() => {
  return state.all_attachments.reduce((acc, curr) => {
    acc[curr.uid] = true;
    return acc;
  }, {});
});
const is_cluster_selected = computed(() => !!props.store.gallery_view_state.filtered_attachment_features?.length || !!props.store.selected_features.length);
const filtered_attachments = computed(() => {
  const { filtered_attachment_features } = props.store.gallery_view_state;
  const selected_features_uids = props.store.selected_features.map(feature => feature.properties.uid);
  return state.all_attachments.filter(attachment =>
    ((!filtered_attachment_features.length || filtered_attachment_features.includes(attachment.feature_uid))
      && (!selected_features_uids.length || selected_features_uids.includes(attachment.feature_uid))
    ));
});

function filterAttachments(all_attachments) {
  const { created_at, type, created_by, classes } = state.filter;

  return all_attachments.filter((attachment) => {
    // Filter by type
    if (type?.value && type?.value !== 'all') {
      const is_raw = attachment.is_raw_image;
      if ((is_raw && type.value !== 'raw_images') || (!is_raw && type.value === 'raw_images')) {
        return false;
      }
    }

    // Filter by created_at
    if (created_at && created_at?.option_value !== 'all_time') {
      if (!attachment?.created_at)
        return false;

      const created_at_date = dayjs(attachment.created_at);
      const start_date = created_at.value[0];
      const end_date = created_at.value[1];

      if (start_date && end_date) {
        if (!created_at_date.isBetween(start_date, end_date, null, '[]'))
          return false;
      }
      else if (start_date) {
        if (!created_at_date.isAfter(start_date))
          return false;
      }
      else if (end_date) {
        if (!created_at_date.isBefore(end_date))
          return false;
      }
    }

    // Filter by created_by
    if (created_by?.value?.length) {
      if (!created_by?.value?.includes(attachment?.created_by))
        return false;
    }

    // Filter by classes
    if (classes?.value?.length) {
      if (!classes?.value?.includes(attachment.feature_type))
        return false;
    }

    return true;
  });
}
function getData() {
  try {
    state.feature_attachments_map = props.store.active_attachments_list().reduce((acc, feature) => {
      const all_feature_attachments = feature.all_feature_attachments;
      acc[feature.feature_uid] = formatAttachments(all_feature_attachments, feature.feature_uid);
      return acc;
    }, {});
    const all_attachments = flatten(Object.values(state.feature_attachments_map));
    state.all_available_attachments = all_attachments;
    state.all_attachments = filterAttachments(all_attachments);
  }
  catch (error) {
    console.error(error);
  }
}

function formatAttachments(attachments) {
  return attachments.map((attachment) => {
    if (!attachment?.owner?.uid)
      attachment.owner = { uid: attachment.created_by };
    return attachment;
  });
}

async function closeSidebar() {
  state.filter = {};
  props.store.set_gallery_state('is_active', false);
  props.store.set_gallery_state('filtered_attachment_features', []);
  await updateAttachmentPoint(props.store, attachment_options.value);
}

async function updateFilter(filter) {
  state.is_loading = true;
  state.filter[filter.uid] = filter;
  getData();
  await updateAttachmentPoint(props.store, attachment_options.value);
  await sleep(200);
  state.selected_attachments_map = {};
  state.is_loading = false;
}

function handleRemoveActiveCluster() {
  props.store.set_gallery_state('filtered_attachment_features', []);
  props.store.set_selected_features([]);
  getData();
  updateAttachmentPoint(props.store, attachment_options.value);
}

async function onGeneratePDFClicked() {
  state.is_pdf_export_complete = false;
  state.terra_pdf_exporter = useFeatureAttachmentsPdfExporter({
    isExporting: () => state.is_pdf_exporting,
    updatePdfExportProgress: (payload) => {
      state.current_export_file_index = payload.current_export_file_index;
      state.total_files_to_export = payload.total_files_to_export;
    },
  });
  const export_attachments = filtered_attachments.value.filter(attachment => state.selected_attachments_map[attachment.uid] || selected_attachments.value.length === 0);
  await state.terra_pdf_exporter.exportPDF(export_attachments, state.filter?.created_at);
  state.is_pdf_exporting = false;
  state.is_pdf_export_complete = true;
  state.current_export_file_index = 1;
  state.total_files_to_export = 1;
}
function onExportClose() {
  state.is_pdf_exporting = false;
  state.is_pdf_export_complete = false;
  state.current_export_file_index = 1;
  state.total_files_to_export = 1;
}
function updateSelectAllStatus() {
  state.select_all_attachments = !state.select_all_attachments;
  if (state.select_all_attachments) {
    filtered_attachments.value.forEach((attachment) => {
      state.selected_attachments_map[attachment.uid] = true;
    });
  }
  else {
    state.selected_attachments_map = {};
  }
}
function updateSelectedAttachments(uid) {
  if (state.selected_attachments_map[uid]) {
    delete state.selected_attachments_map[uid];
  }
  else {
    state.selected_attachments_map[uid] = true;
  }
  state.select_all_attachments = (selected_attachments.value.length === filtered_attachments.value.length);
}
function onClearSelection() {
  state.selected_attachments_map = {};
}

watchEffect(() => {
  const { is_active } = props.store.gallery_view_state;
  if (is_active) {
    const app = document.getElementById('app');
    setTimeout(() => {
      app.removeAttribute('inert');
    }, 100);
  }
});

watch(() => props.store.active_attachments_list(), () => {
  if (props.store.gallery_view_state.is_active)
    getData();
}, { deep: true });

watch(() => Object.keys(props.store.filtered_features_hash), async (val, old_val) => {
  const { is_active, active_attachment } = props.store.gallery_view_state;
  if (is_active) {
    if (isEqual(val, old_val))
      return;
    state.is_loading = !active_attachment;
    getData();
    await updateAttachmentPoint(props.store, attachment_options.value);
    state.is_loading = false;
  }
}, { deep: true });

watch(() => props.store.gallery_view_state.active_attachment, (val) => {
  if (val)
    props.store.set_selected_features([props.store.features_hash[val.feature_uid]]);
});
</script>

<template>
  <HawkSidebar
    :show="store.gallery_view_state.is_active"
    width="450" position="right" addition_classes="!top-16 !h-[calc(100vh-48px)] rounded-l-lg !p-0" :emit_close="false"
  >
    <HawkExportToast v-if="state.is_pdf_exporting" :submit="onGeneratePDFClicked" @cancel="onExportClose" @complete="onExportClose" @close="onExportClose">
      <template v-if="!state.is_pdf_export_complete" #title>
        <div>
          {{ $t('Exporting PDF') }}<span v-if="state.total_files_to_export > 1">: {{ state.current_export_file_index }}/{{ state.total_files_to_export }}</span>
        </div>
      </template>
      <template v-else #title>
        {{ $t('Exported PDF') }}
      </template>
    </HawkExportToast>
    <div v-if="state.is_loading" class="relative h-full flex flex-col flex-1 px-4 sm:px-6 pb-6">
      <hawk-loader />
    </div>
    <div v-else class="relative h-full flex flex-col flex-1 pt-6">
      <AttachmentDetails
        v-if="store.gallery_view_state.active_attachment"
        :store="store"
        :available_attachments_map="available_attachments_map"
        @active-attachment="store.set_gallery_state('active_attachment', $event)"
        @close="store.set_gallery_state('active_attachment', null)"
        @close-slider="store.set_gallery_state('active_attachment', null); closeSidebar()"
      >
        <template #feature_details>
          <slot name="feature_details" />
        </template>
      </AttachmentDetails>
      <template v-else>
        <div class="w-full px-4 sm:px-6">
          <div v-if="is_cluster_selected" class="flex items-center justify-between gap-2 w-full">
            <div class="flex items-center gap-2">
              <div class="w-5 h-5 cursor-pointer" @click="handleRemoveActiveCluster">
                <IconHawkArrowLeft class="w-5 h-5" />
              </div>
              <div class="text-lg font-semibold text-gray-900">
                {{ $t('Back') }} ({{ filtered_attachments.length }})
              </div>
            </div>
          </div>
          <div v-else class="flex items-center justify-between min-w-[330px] max-w-[360px] bg-white">
            <div class="text-lg font-semibold text-gray-900 flex items-center">
              {{ $t('Gallery') }} ({{ filtered_attachments.length }})
              <HawkFeaturedIcon
                v-if="store.gallery_view_state.show_load_progress_attachments"
                v-tippy="{ content: $t('Refresh to see new attachments'), placement: 'top' }"
                theme="light-circle-outline" color="error" class="ml-2"
              >
                <IconHawkAlertTriangle />
              </HawkFeaturedIcon>
            </div>
            <div>
              <IconHawkX
                class="w-6 h-6 cursor-pointer text-gray-600 hover:text-gray-900  outline-none"
                tabindex="0"
                @click="closeSidebar"
              />
            </div>
          </div>
        </div>
        <div class="mt-2">
          <div class="flex items-center justify-between px-6 gap-x-2">
            <div class="flex items-center gap-x-2">
              <div v-if="selected_attachments.length" class="flex items-center justify-center">
                <HawkCheckbox
                  v-if="show_select_checkbox"
                  class="text-sm font-gray-900"
                  :model-value="state.select_all_attachments"
                  @click="updateSelectAllStatus()"
                >
                  {{ $t('Select all') }} <span v-if="selected_attachments.length">({{ selected_attachments.length }} {{ $t('selected') }})</span>
                </HawkCheckbox>
                <HawkButton v-if="selected_attachments.length" class="!p-0 ml-4" type="plain" @click="onClearSelection">
                  {{ $t('Clear') }}
                </HawkButton>
              </div>
              <div v-else class="flex items-center gap-x-2 flex-wrap">
                <GalleryFilters
                  :is-therm-viewer="is_therm_viewer"
                  :filter-state="state.filter"
                  :all-attachments="state.all_available_attachments"
                  @apply-filters="updateFilter($event)"
                />
              </div>
            </div>
            <HawkButton v-if="!is_therm_viewer && filtered_attachments.length" class="font-semibold text-gray-700" size="xs" type="link" @click="state.is_pdf_exporting = true">
              {{ $t('Export') }}
            </HawkButton>
          </div>
          <hawk-attachments-grid
            v-if="filtered_attachments?.length"
            :key="filtered_attachments?.join(',')"
            class="m-4"
            use_virtualization
            :items="sortAttachments(filtered_attachments)"
            :image_dimensions="[160, 160]"
            :show_delete="false"
            :selected_attachments="selected_attachments.map(uid => ({ uid }))"
            :scroller_classes="['h-[calc(100vh-166px)]']"
            show_attachments_list_preview
            @attachment-clicked="store.set_gallery_state('active_attachment', $event)"
          >
            <template
              v-for="attachment in filtered_attachments" :key="attachment.uid"
              #[`attachment-details-left-content-${attachment.uid}`]
            >
              <div v-if="show_select_checkbox">
                <HawkCheckbox
                  :id="attachment.uid"
                  :model-value="Boolean(state.selected_attachments_map[attachment.uid])"
                  :class="{ visible: !!selected_attachments.length }"
                  @input="updateSelectedAttachments(attachment.uid)" @click.stop=""
                />
              </div>
            </template>
          </hawk-attachments-grid>
        </div>
      </template>
    </div>
  </HawkSidebar>
  <div>
    <div class="relative flex flex-col mr-2.5">
      <MapToolIcons
        style="position: relative"
        :tools="gallery_tools"
        :active_tool="store.gallery_view_state.is_active ? 'gallery-view' : ''"
        :horizontal_icons="true"
      />
    </div>
  </div>
</template>
