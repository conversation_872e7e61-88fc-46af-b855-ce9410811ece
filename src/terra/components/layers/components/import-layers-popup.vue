<script setup>
import { onMounted, watch } from 'vue';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import { isFileExtensionAllowed, sleep } from '~/common/utils/common.utils.js';
import FeatureTypes from '~/terra/components/feature-type-groups/feature-types.vue';
import { useTerraImportHelperComposable } from '~/terra/components/layers/components/terra-import-helper.composable.js';
import { useTerraStore } from '~/terra/store/terra.store';
import { useTerraHelperComposable } from '~/terra/utils/helper-composable';

const emits = defineEmits(['close']);

const terra_store = useTerraStore();

const { $t, $services, $toast, auth_store, route } = useCommonImports();
const { parseFeature, getStyles, preProcessGeojson } = useTerraHelperComposable();
const { parseGeoJSON, parseKML } = useTerraImportHelperComposable();

let turf;

const form = ref({
  file: null,
  merge_features: false,
  categorize_by_property: null,
  advance_options: true,
});

const state = reactive({
  features: [],
  features_key_values: {},
  layer_sublayer_groups: {},
  is_loading: false,
  error_type: '',
  group_project_uid: {},
});

const table_details = computed(() => {
  let invalid_features_count = 0;

  const data = [];
  const columns = [
    { id: 'layer', accessorKey: 'layer', header: $t('Layer') },
    { id: 'sublayer', accessorKey: 'sublayer', header: $t('Sublayer') },
    { id: 'count', accessorKey: 'count', header: $t('Count'), size: 80 },
  ];
  Object.entries(state.layer_sublayer_groups).forEach(([layer, sublayer_map]) => {
    Object.entries(sublayer_map).forEach(([sublayer, features]) => {
      if (layer === 'undefined' || sublayer === 'undefined') {
        invalid_features_count += features.length;
      }
      else {
        data.push({
          layer,
          sublayer,
          count: features.length,
          features,
        });
      }
    });
  });
  return { invalid_features_count, data, columns };
});

function groupByLayerAndSublayer() {
  const grouped_by_layer_sublayer = {};

  state.features.forEach((f) => {
    const { Layer, Sublayer } = f?.properties;

    // Initialize Layer if not exists
    if (!grouped_by_layer_sublayer[Layer]) {
      grouped_by_layer_sublayer[Layer] = {};
    }

    // Initialize Sublayer if not exists under Layer
    if (!grouped_by_layer_sublayer[Layer][Sublayer]) {
      grouped_by_layer_sublayer[Layer][Sublayer] = [];
    }

    grouped_by_layer_sublayer[Layer][Sublayer].push(f);
  });

  state.layer_sublayer_groups = grouped_by_layer_sublayer;
}

async function onImportFile() {
  const file_data = form.value.file;
  state.features = [];
  state.layer_sublayer_groups = {};
  state.features_key_values = {};
  state.group_project_uid = {};

  if (!isFileExtensionAllowed(file_data?.name))
    return;

  state.is_loading = true;
  const reader = new FileReader();
  reader.onload = async function (e) {
    if (!turf)
      turf = await import('@turf/turf');
    let features = [];
    try {
      if (['.geojson', '.json'].some(type => file_data.name.includes(type)))
        features = await parseGeoJSON(e.target.result);
      else
        features = await parseKML(e.target.result, turf);

      // parse data
      for (const i in features) {
        const feature = parseFeature(features[i]);
        feature.properties.hierarchyProperties = null;
        feature.properties.name = String(feature.properties?.name || '');
        features[i] = feature;
      }

      state.features = features;
      if (state.features?.length)
        state.features_key_values = terra_store.extra_properties_group_by({ features: state.features });
      else
        state.features_key_values = {};
      groupByLayerAndSublayer();
    }
    catch (error) {
      $toast({
        title: 'Error parsing features',
        type: 'error',
      });
      logger.error(error);
      state.is_loading = false;
      return;
    }

    state.is_loading = false;
  };
  reader.readAsText(file_data);
}

function updatedFeatures(features, project_id = null) {
  features.map((f) => {
    if (f.properties.project && !form.value.merge_features)
      delete f.properties?.project;
    if (f.properties?.extraProperties?.[form.value.categorize_by_property]) {
      const key = form.value.categorize_by_property;
      const value = f.properties?.extraProperties?.[key];
      const feature_type = state.features_key_values?.[key]?.[value]?.featureType;
      if (feature_type) {
        let old_feature_type_id = null;
        if (form.value.merge_features)
          old_feature_type_id = terra_store.features_hash?.[f.properties?.uid]?.properties?.featureTypeId || null;
        f.properties.oldfeatureTypeId = old_feature_type_id;
        f.properties.featureType = feature_type.uid;
        f.properties.featureTypeId = feature_type.id;
      }
    }
    return f;
  });
  const update_features = [];
  const create_features = [];
  features.forEach((feature) => {
    if (form.value.merge_features && terra_store.features_hash[feature?.properties?.uid]) {
      update_features.push(feature);
    }
    else {
      delete feature.properties?.uid;
      delete feature.properties?.project;
      create_features.push(feature);
    }
    feature.properties.project = project_id;
  });
  return { create_features, update_features };
}

async function createGroup(name) {
  const group_response = await $services.groups.post({
    asset_id: route.params.asset_id,
    container_id: terra_store.container.uid,
    body: {
      project_group: {
        organization: auth_store?.current_organization?.uid,
        name,
      },
    },
  });
  return group_response?.data?.project_group?.uid;
}

async function createProject(name, group_id) {
  const project_response = await $services.projects.post({
    asset_id: route.params.asset_id,
    container_id: terra_store.container.uid,
    group_id,
    body: {
      project: {
        name,
        organization: auth_store?.current_organization?.uid,
      },
    },
  });
  return project_response?.data?.project?.uid;
}

async function addOrUpdateFeatures(project_id, features) {
  const container_id = terra_store.container.uid;
  const { create_features, update_features } = updatedFeatures(features, project_id);
  if (create_features?.length) {
    const geojson = preProcessGeojson({ type: 'FeatureCollection', features: create_features });
    await $services.features.post({
      uid: project_id,
      body: geojson,
      attribute: `container/${container_id}/project/${project_id}`,
    });
  }
  else {
    const geojson = preProcessGeojson({ type: 'FeatureCollection', features: update_features });
    await $services.features.put({
      uid: project_id,
      body: geojson,
      attribute: `container/${container_id}/project/${project_id}`,
    });
  }
}

async function handleUpload() {
  const { data } = table_details.value;
  const { groups } = terra_store.container;

  const group_name_map = {};
  Object.values(groups || {}).forEach((group) => {
    const project_names_map = {};
    Object.values(group.projects || {}).forEach((project) => {
      project_names_map[project.name] = {
        uid: project.uid,
      };
    });
    group_name_map[group.name] = {
      uid: group.uid,
      project_names_map,
    };
  });

  const active_project_uids = new Set();

  const group_data_by_layer = data.reduce((acc, item) => {
    const { layer } = item;
    if (!acc[layer])
      acc[layer] = [];
    acc[layer].push(item);
    return acc;
  }, {});

  // Process each group sequentially
  for (const [layer, items] of Object.entries(group_data_by_layer)) {
    let group_uid = group_name_map[layer]?.uid;

    // Create group if it doesn't exist
    if (!group_uid) {
      group_uid = await createGroup(layer);
      await sleep(1000);
    }

    // Process each project (sublayer) in this group
    for (const item of items) {
      const { sublayer, features } = item;

      let project_uid = group_name_map[layer]?.project_names_map?.[sublayer]?.uid;

      // Create project if it doesn't exist
      if (!project_uid) {
        project_uid = await createProject(sublayer, group_uid);
        await sleep(1000);
      }

      // Add or update features
      if (group_uid && project_uid) {
        await addOrUpdateFeatures(project_uid, features);

        // Update local maps
        group_name_map[layer] = {
          uid: group_uid,
          project_names_map: {
            ...(group_name_map[layer]?.project_names_map || {}),
            [sublayer]: {
              uid: project_uid,
            },
          },
        };

        terra_store.active_projects_map[project_uid] = {
          ortho_enabled: true,
          features_enabled: true,
        };

        active_project_uids.add(project_uid);
      }
    }
  }

  await sleep(1000);
  await terra_store.set_container({
    uid: terra_store.container.uid,
    forceUpdate: true,
    keep_project_map: true,
  });

  const projects = Array.from(active_project_uids).map(project_uid => terra_store.active_projects_data_map({ all_projects: true })[project_uid]);

  await terra_store.set_projects_essentials({
    projects,
    forceUpdate: true,
  });

  await terra_store.update_map_features_and_polygon();
  await terra_store.fly_to_project({
    project: projects[projects.length - 1],
  });
}

async function submit() {
  try {
    state.is_loading = true;

    await handleUpload();

    state.is_loading = false;
    emits('close');
  }
  catch (err) {
    console.error(err);
    $toast({
      title: 'Import failed',
      text: 'Please try again',
      type: 'error',
    });
    state.is_loading = false;
  }
}

// -------------------------------- Watchers -------------------------------- //
watch(() => form.value.categorize_by_property, (_curr, prev) => {
  const values_object = state.features_key_values?.[prev] || {};
  Object.keys(values_object).forEach((value) => {
    state.features_key_values[prev][value].featureType = null;
  });
});

onMounted(async () => {
  turf = await import('@turf/turf');
});
</script>

<template>
  <hawk-modal-container :width="700" content_class="rounded-lg w-[700px]" :options="{ clickToClose: !state.is_loading, escToClose: false }">
    <Vueform
      v-model="form" sync size="sm" :display-errors="false"
      :columns="{
        lg: {
          container: 12,
          label: 4,
          wrapper: 12,
        },
      }"
      :endpoint="submit"
    >
      <div class="col-span-12">
        <hawk-modal-header class="!px-6 !py-4" @close="emits('close')">
          <template #title>
            <div class="flex items-center">
              {{ $t('Import layers') }}
            </div>
          </template>
        </hawk-modal-header>
        <hawk-modal-content>
          <!-- File Upload Section -->
          <FileElement
            name="file"
            class="mb-3"
            :label="$t('Choose file')"
            :presets="['hawk_file_element']"
            :options="{
              clickable_text: $t('Upload'),
              text: $t('KML or GeoJSON files'),
              description: $t('.kml, .geojson formats supported'),
            }"
            accept=".geojson, .kml, .json"
            :auto="false"
            :drop="true"
            :use_uppy="false"
            @change="onImportFile"
          >
            <template #description>
              <div v-if="!form.file" class="text-xs text-gray-700 mt-2">
                <span class="font-medium">{{ $t('Note') }}: </span>
                {{ $t('Upload a geojson file with features containing layers, sublayers properties to automatically create/import into the respective locations') }}.
              </div>
            </template>
          </FileElement>
          <HawkAlertBanner
            v-if="table_details.invalid_features_count > 0"
            color="error"
          >
            <template #icon>
              <IconHawkAlertTriangle />
            </template>
            <template #content>
              "{{ $t('Layer') }}" {{ $t('and') }} "{{ $t('Sublayer') }}" {{ $t('do not exist in the uploaded file') }}.
            </template>
          </HawkAlertBanner>
          <div v-if="table_details.data?.length">
            <div class="max-h-[315px] scrollbar border-b">
              <HawkTable
                :columns="table_details.columns"
                :data="table_details.data"
                :show_menu_header="false"
                :is_gapless="true"
                :disable_resize="true"
              />
            </div>
            <div class="text-xs text-gray-700 mt-2">
              <span class="font-medium">{{ $t('Note') }}:</span>
              {{ $t('Vectors will be imported to above mentioned Layer and Sublayer. New layers and sublayers will be created if they are not available.') }}
            </div>
          </div>
          <CheckboxElement
            class="my-4 text-primary font-semibold"
            name="advance_options"
            :add-class="{
              input: 'hidden',
            }"
          >
            <div class="flex items-center text-[#004EEB] ml-[-0.5rem]">
              <IconHawkChevronRight v-if="form && !form.advance_options" class="w-5 text-primary-700" />
              <IconHawkChevronDown v-else class="w-5 text-primary-700" />
              {{ $t('Advanced options') }}
            </div>
          </CheckboxElement>
          <CheckboxElement
            name="merge_features"
            class="mb-5"
            :conditions="[
              ['advance_options', true],
            ]"
            :default="false"
            :label="$t('Merge Features')"
          />
          <SelectElement
            name="categorize_by_property"
            class="mb-5"
            open-direction="top"
            :conditions="[
              ['advance_options', true],
            ]"
            :placeholder="$t('Select')"
            :label="$t('Classify by property')"
            :items="Object.keys(state.features_key_values)"
            :native="false"
          />
          <template v-if="form.advance_options && form.categorize_by_property">
            <div v-for="(value_data, value) in state.features_key_values[form.categorize_by_property]" :key="value">
              <div class="grid grid-cols-12 grid-flow-col gap-2 mb-2 text-sm items-center">
                <div class="col-span-5">
                  {{ value }}
                </div>
                <div class="col-span-1">
                  <IconHawkArrowRight />
                </div>
                <div class="col-span-6">
                  <HawkMenu additional_trigger_classes="!ring-0 !border-0" additional_dropdown_classes="!w-80 !max-h-60 scrollbar">
                    <template #trigger>
                      <HawkButton type="plain" size="sm">
                        <div
                          v-if="value_data?.featureType"
                          class="cursor-pointer w-3 h-3 rounded-full bg-gray-400"
                          :style="getStyles(value_data?.featureType)"
                        />
                        <div class="text-sm font-medium">
                          <HawkText :content="value_data?.featureType?.name || 'Select feature'" :length="32" />
                        </div>
                      </HawkButton>
                    </template>
                    <template #content="{ close }">
                      <FeatureTypes
                        type="dropdown" :dropdown_value="feature_type" @select="($event) => {
                          state.features_key_values[form.categorize_by_property][value].featureType = $event;
                          close();
                        }"
                      />
                    </template>
                  </HawkMenu>
                </div>
              </div>
            </div>
          </template>
        </hawk-modal-content>
        <hawk-modal-footer class="flex justify-between items-center">
          <template #right>
            <!-- Footer -->
            <div class="flex justify-end items-center">
              <hawk-button
                class="mr-4 font-bold"
                type="outlined"
                @click="emits('close')"
              >
                {{ $t('Cancel') }}
              </hawk-button>
              <ButtonElement
                :button-label="$t('Import')"
                :disabled="!table_details.data?.length"
                :submits="true"
              />
            </div>
          </template>
        </hawk-modal-footer>
      </div>
    </Vueform>
  </hawk-modal-container>
</template>

<style scoped lang="scss">
.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
