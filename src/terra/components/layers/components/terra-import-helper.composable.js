import toGeoJson from '@mapbox/togeojson';

export function useTerraImportHelperComposable() {
  function arraysEqual(a, b) {
    if (a === b)
      return true;
    if (a == null || b == null)
      return false;
    if (a.length !== b.length)
      return false;
    for (let i = 0; i < a.length; ++i) {
      if (a[i] !== b[i])
        return false;
    }
    return true;
  }

  async function parseGeoJSON(data) {
    try {
      const geojson = JSON.parse(data);
      const features = geojson?.features || [];
      return features;
    }
    catch (error) {
      throw new Error(`Error parsing GeoJSON: ${error.message}`);
    }
  }

  async function parseKML(data, turf) {
    try {
      const kml = new DOMParser().parseFromString(data, 'text/xml');
      const kml_converted = toGeoJson.kml(kml, { styles: false });

      const features = (kml_converted?.features || []).map((feature) => {
        if (feature.geometry.geometries) {
          // Extract geometries from GeometryCollection
          const geometries = feature.geometry.geometries;
          let multiGeometry;
          geometries.forEach((geometry) => {
            const turfGeometry = turf.clone(geometry);
            if (!multiGeometry)
              multiGeometry = turfGeometry;
            else
              multiGeometry = turf.union(multiGeometry, turfGeometry);
          });
          feature.geometry = multiGeometry.geometry;
        }
        if (feature.geometry.type === 'Polygon') {
          feature.geometry.coordinates.forEach((ring) => {
            // Ensure the first and last points are the same
            if (!arraysEqual(ring[0], ring[ring.length - 1]))
              ring.push(ring[0]); // Add the first point to the end
          });
        }

        return feature;
      });
      return features;
    }
    catch (error) {
      throw new Error(`Error parsing KML: ${error.message}`);
    }
  }

  return { parseGeoJSON, parseKML };
}
