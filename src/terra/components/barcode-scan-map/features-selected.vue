<script setup>
import { isEqual, sortBy } from 'lodash-es';
import { computed, reactive, watch } from 'vue';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import FeatureActionsToolbar from '~/terra/components/feature-details/actions-toolbar/feature-actions-toolbar.vue';
import FeatureDetailsSlider from '~/terra/components/feature-details/feature-details-slider.vue';
import { createSerialNumberImage } from '~/terra/composables/barcode-scan-helper.composable';
import { useTerraStore } from '~/terra/store/terra.store';
import { useFeatureActions } from '~/terra/utils/feature-actions.composable.js';

const props = defineProps({
  sidebarState: {
    type: Object,
    required: true,
  },
});
const { $t } = useCommonImports();
const terra_store = useTerraStore();
const { handleClearSerialNumbers, handleEditFeature, handleCopy, moveSelectedFeatures, deleteSelectedFeatures, handleExportSerialNumbers, handleInverseSerialNumbers } = useFeatureActions();

const state = reactive({
  selected_cells: [],
});
const footer_container = ref(null);
const { width: containerWidth } = useElementSize(footer_container);

const available_toolbar_width = computed(() => {
  const availableWidth = containerWidth.value;
  return availableWidth;
});

const footer_style = computed(() => {
  return `${
    props.sidebarState?.active_menu?.length
      ? `width:calc(100vw - 380px); left:380px;`
      : `width:calc(100vw - 80px); left:80px;`
  }`;
});
const features_list = computed(() => sortBy(terra_store.features_on_map, ['properties.name']));
const feature_current_index = computed(() => {
  const selected_feature = terra_store.selected_features[0];
  if (!selected_feature)
    return -1;
  return features_list.value.findIndex(feature => feature.properties.uid === selected_feature.properties.uid);
});
const is_bulk_selected = computed(() => {
  return terra_store.selected_features.length > 1;
});

const selected_feature = computed(() => {
  return terra_store.features_hash[terra_store.selected_features[0].properties.uid];
});

const feature_layout_details = computed(() => {
  const { extraProperties } = selected_feature.value.properties;
  const num_nodules_horizontal = Number.parseInt(extraProperties?.num_modules_horizontal);
  const num_modules_vertical = Number.parseInt(extraProperties?.num_modules_vertical);

  return {
    num_nodules_horizontal,
    num_modules_vertical,
    is_horizontal_layout: num_nodules_horizontal < num_modules_vertical,
    is_valid_data: !Number.isNaN(num_nodules_horizontal) && !Number.isNaN(num_modules_vertical),
  };
});

const slider_styles = computed(() => {
  const feature_layout = feature_layout_details.value;
  if (feature_layout.is_horizontal_layout) {
    return `height:calc(100vh - 65px);`;
  }
  else {
    return footer_style.value;
  }
});

const slider_classes = computed(() => {
  const feature_layout = feature_layout_details.value;
  if (feature_layout.is_horizontal_layout) {
    return 'right-0 w-[400px]';
  }
  else {
    return 'bottom-0 h-[320px]';
  }
});

const selection_tools = computed(() => {
  return [
    {
      uid: 'select_all',
      label: $t('Select all'),
      on_click: () => {
        state.selected_cells = getSerialNumberKeysWithValues();
      },
    },
    {
      uid: 'invert_selection',
      label: $t('Invert selection'),
      on_click: () => {
        state.selected_cells = getSerialNumberKeysWithValues().filter(item => !state.selected_cells.includes(item));
      },
    },
  ];
});

const hawk_menu_items = computed(() => {
  const permissions = {
    disabled: !terra_store.check_permission('modify_features'),
    disabled_reason: $t('You don\'t have permissions to perform this action.'),
  };
  return [
    {
      uid: 'edit',
      label: $t('Edit'),
      on_click: handleEditFeature,
    },
    {
      uid: 'copy',
      label: $t('Copy'),
      on_click: handleCopy,
    },
    {
      uid: 'move',
      label: $t('Move'),
      on_click: moveSelectedFeatures,
    },
    {
      uid: 'delete',
      label: $t('Delete'),
      on_click: deleteSelectedFeatures,
    },
  ].map((item) => {
    return {
      ...item,
      ...permissions,
    };
  });
});
const actions_menu_items = computed(() => {
  const permission = terra_store.check_permission('modify_feature_properties');
  return [
    {
      uid: 'export-excel',
      label: $t('Export as excel'),
      on_click: () => {
        handleExportSerialNumbers();
      },
    },
    {
      uid: 'download-image',
      label: $t('Download image'),
      on_click: () => {
        createSerialNumberImage(selected_feature.value.properties.name || 'Untitled');
      },
    },
    {
      uid: 'inverse-serial-numbers',
      label: $t('Inverse serial numbers'),
      disabled: !terra_store.check_permission('modify_scan_data'),
      tooltip: !terra_store.check_permission('modify_scan_data') && $t('You don\'t have permissions to perform this action.'),
      on_click: handleInverseSerialNumbers,
    },
    ...(state.selected_cells.length > 0
      ? [{
          uid: 'clear_selected',
          label: $t('Clear'),
          disabled: !permission,
          tooltip: permission ? '' : $t('You don\'t have permissions to perform this action.'),
          on_click: () => {
            const project = selected_feature.value.properties.project;
            const feature_uid = selected_feature.value.properties.uid;
            const payload = {
              [project]: {
                [feature_uid]: state.selected_cells,
              },
            };
            handleClearSerialNumbers({
              clear_selected: true,
              payload,
              on_submit: () => {
                state.selected_cells = [];
              },
            });
          },
        }]
      : [{
          uid: 'clear_all',
          label: $t('Clear all'),
          disabled: !permission,
          tooltip: permission ? '' : $t('You don\'t have permissions to perform this action.'),
          on_click: handleClearSerialNumbers,
        }]),
  ];
});

const get_barcode_grid_data = computed(() => {
  const feature_layout = feature_layout_details.value;
  if (!feature_layout.is_valid_data)
    return null;

  const duplicate_serial_number_key_map = terra_store.feature_duplicates_map?.[selected_feature.value?.properties?.uid] || {};
  const { extraProperties } = selected_feature.value.properties;
  let disabled_modules = extraProperties?.disabled_modules || [];
  if (!Array.isArray(disabled_modules)) {
    disabled_modules = disabled_modules.slice(1, -1).split(',').map(v => v.trim());
  }
  const matrix = [];

  const header_row = [{ is_header: true, value: '', class: 'w-10 h-8' }];
  for (let i = 1; i <= feature_layout.num_nodules_horizontal; i++) {
    header_row.push({ is_header: true, value: i, is_column_header: true, class: 'w-[150px] h-8' });
  }
  matrix.push(header_row);

  for (let i = 0; i < feature_layout.num_modules_vertical; i++) {
    const data_row = [{ is_header: true, value: i + 1, class: 'w-10 h-[52px]' }];
    for (let j = 0; j < feature_layout.num_nodules_horizontal; j++) {
      const serial_number = extraProperties?._serial_numbers?.[`${i}:${j}`] || '';

      data_row.push({
        is_header: false,
        row: i,
        column: j,
        serial_number_key: `${i}:${j}`,
        value: serial_number,
        is_duplicate: !!duplicate_serial_number_key_map[`${i}:${j}`],
        is_disabled: disabled_modules?.includes(`${i + 1}:${j + 1}`) ?? false,
      });
    }
    matrix.push(data_row);
  }

  return matrix;
});

const last_scanned_date = computed(() => selected_feature.value.properties?.extraProperties?._serial_numbers_last_updated_at);

function handleSelectCell(cell) {
  if (cell.value && state.selected_cells.includes(cell.serial_number_key)) {
    state.selected_cells = state.selected_cells.filter(item => item !== cell.serial_number_key);
  }
  else if (cell.value) {
    state.selected_cells.push(cell.serial_number_key);
  }
}
function getSerialNumberKeysWithValues() {
  return get_barcode_grid_data.value
    .flat()
    .filter(item => item.serial_number_key && item.value)
    .map(item => item.serial_number_key);
}
function handleNavigation(direction) {
  const current_index = feature_current_index.value;
  const next_index = direction === 'next' ? current_index + 1 : current_index - 1;
  if (next_index >= 0 && next_index < features_list.value.length) {
    const next_feature = features_list.value[next_index];
    terra_store.selected_features = [next_feature];
  }
}

watch(() => terra_store.selected_features.map(f => f.properties.uid), (new_val, old_val) => {
  if (!isEqual(new_val, old_val)) {
    state.selected_cells = [];
  }
});
</script>

<template>
  <div
    v-if="is_bulk_selected"
    ref="footer_container"
    :style="footer_style"
    class="fixed z-[99] bottom-3 flex justify-center items-center space-x-4 w-auto"
  >
    <FeatureActionsToolbar
      class="mb-0.5"
      :available-width="available_toolbar_width"
    />
  </div>
  <div
    v-else-if="terra_store.selected_features.length === 1"
    :style="slider_styles"
    class="fixed z-[99] bg-white border"
    :class="slider_classes"
  >
    <div class="flex items-center flex-wrap justify-between px-4 py-2 border-b border-gray-300">
      <div class="flex items-center flex-wrap gap-3">
        <div id="feature_name_title" class="text-md font-semibold">
          {{ selected_feature.properties.name }}
        </div>
        <FeatureDetailsSlider
          v-if="!state.selected_cells.length"
          :feature="selected_feature"
          :hawk_menu_items="hawk_menu_items"
        >
          <template #trigger="{ action }">
            <HawkButton
              type="link" class="text-sm"
              @click="action()"
            >
              {{ $t('Details') }} <IconHawkArrowRight class="-ml-0.5 w-5 h-5" />
            </HawkButton>
          </template>
        </FeatureDetailsSlider>
      </div>
      <div class="flex items-center flex-wrap gap-2">
        <div v-if="!feature_layout_details.is_horizontal_layout && !state.selected_cells.length" class="flex flex-row">
          <HawkButton type="text" :disabled="feature_current_index === 0" @click="handleNavigation('prev')">
            <IconHawkChevronLeft />{{ $t('Previous') }}
          </HawkButton>
          <HawkButton type="text" :disabled="feature_current_index === features_list.length - 1" @click="handleNavigation('next')">
            {{ $t('Next') }}<IconHawkChevronRight />
          </HawkButton>
        </div>
        <HawkMenu v-if="state.selected_cells.length" position="fixed" additional_trigger_classes="!ring-0 !border-0" :items="selection_tools">
          <template #trigger="{ open }">
            <div class="flex items-center border-2 border-gray-200 rounded-lg">
              <div class="text-xs font-medium text-gray-600 py-1.5 px-3" :class="{ 'bg-gray-100': open }">
                {{ state.selected_cells.length }} {{ $t('selected') }}
              </div>
              <div class="cursor-pointer border-l-2 border-gray-200 py-1.5 px-2" @click="state.selected_cells = []">
                <IconHawkXClose class="w-4 h-4" />
              </div>
            </div>
          </template>
        </HawkMenu>

        <HawkMenu
          :items="actions_menu_items"
          position="fixed"
          :has_bordered_trigger="false"
          additional_trigger_classes="!ring-0 p-0"
        />
        <HawkButton type="plain" size="sm" icon @click="terra_store.selected_features = []">
          <IconHawkXClose class="w-5 h-5 text-gray-600" />
        </HawkButton>
      </div>
    </div>
    <template v-if="get_barcode_grid_data">
      <div id="serial_numbers_container" class="overflow-auto p-4" :class="feature_layout_details.is_horizontal_layout ? 'h-[calc(100%-140px)]' : 'w-full h-[calc(100%-100px)]'">
        <div class="flex flex-col gap-3 w-max relative">
          <div
            class="absolute bg-gray-100 rounded-lg w-auto top-[30px] left-[36px] bottom-[-14px] right-[-12px] z-0"
          />
          <div
            v-for="(row, rowIndex) in get_barcode_grid_data"
            :key="`row-${rowIndex}`"
            class="flex gap-2"
          >
            <div
              v-for="(cell, colIndex) in row"
              :key="`cell-${rowIndex}-${colIndex}`"
            >
              <div
                v-if="cell.is_header"
                class="flex items-center justify-center"
                :class="cell.class"
              >
                <span class="text-xs font-semibold">
                  {{ cell.value }}
                </span>
              </div>
              <div
                v-else
                class="flex items-center justify-center border-2 relative rounded p-2 w-[150px] h-[52px]"
                :class="[
                  cell.class,
                  cell.is_disabled ? 'cursor-not-allowed' : 'cursor-pointer',
                  (cell.is_disabled ? 'bg-gray-100 border-gray-400' : (cell.value ? 'bg-success-100 border-success-200' : 'bg-error-100 border-error-200')),
                  state.selected_cells.includes(cell.serial_number_key) ? '!border-primary-600' : '',
                ]"
                @click="handleSelectCell(cell)"
              >
                <span class="break-all text-xs font-medium">
                  {{ cell.is_disabled ? 'No module' : cell.value }}
                </span>
                <IconHawkAlertTriangle v-if="cell.is_duplicate" class="absolute bottom-1 right-1 w-3 h-3 text-warning-500" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-if="feature_layout_details.is_horizontal_layout" class="flex items-center mt-2 justify-center">
        <div class="flex flex-row">
          <HawkButton type="text" :disabled="feature_current_index === 0" @click="handleNavigation('prev')">
            <IconHawkChevronLeft />{{ $t('Previous') }}
          </HawkButton>
          <HawkButton type="text" :disabled="feature_current_index === features_list.length - 1" @click="handleNavigation('next')">
            {{ $t('Next') }}<IconHawkChevronRight />
          </HawkButton>
        </div>
      </div>
      <div id="feature_last_scanned_date" class="text-xs text-gray-600 pl-7 pt-4">
        {{ $t('Last updated') }}:
        <span class="text-xs font-medium text-gray-900 pl-1">{{ last_scanned_date ? $date(last_scanned_date, 'D MMMM YYYY, h:mm a') : '--' }}</span>
      </div>
    </template>
    <div v-else class="p-4 text-center text-gray-500">
      {{ $t('Not configured for Barcode scan') }}
    </div>
  </div>
</template>
