<!-- eslint-disable vue/prop-name-casing -->
<script setup>
import { getFileExtension } from '~/common/utils/common.utils.js';
import { useDocumentCrud } from '~/dms/composables/document-crud.composable';
import { useDocumentTools } from '~/plans/composables/useDocumentTools';
import { useDocumentViewer } from '~/plans/composables/useDocumentViewer';

const props = defineProps({
  document: {
    type: Object,
    default: () => ({}),
  },
  show_viewer: {
    type: Boolean,
    default: false,
  },
  viewerOptions: {
    type: Object,
    default: () => ({}),
  },
  download_with_annotation: {
    type: Boolean,
  },
});
const emit = defineEmits(['closeAttachment']);
const $t = inject('$t');
const $toast = inject('$toast');
const document_crud = useDocumentCrud();

const { setup_core } = useDocumentViewer({});

const is_setting_up_document_viewer = ref(false);

const state = reactive({
  modified_attachment: null,
});

if (!window?.Core)
  setup_core().then(() => is_setting_up_document_viewer.value = true);

const document_viewer_instance = ref(null);

watch(() => props.show_viewer, async (value) => {
  if (value)
    openAttachment();
});

function documentViewerInstance(instance) {
  document_viewer_instance.value = instance;
}

async function openAttachment() {
  if (getSupportedAttachment(props.document)) {
    state.modified_attachment = {
      uid: props.document.uid,
      url: props.document.presigned_url,
      file_name: props.document.file_name,
    };
  }
  else {
    emit('closeAttachment');
    $toast({
      title: $t('Unsupported file'),
      text: $t('This file is not supported for viewing on the platform. Download to view it.'),
      type: 'error',
      position: 'bottom-right',
    });
  }
}

function getSupportedAttachment(file) {
  const extension = getFileExtension(file.file_name || file.name);
  if (!window.Core)
    return true;

  return window.Core.SupportedFileFormats.CLIENT.includes(extension?.toLowerCase());
}

function onDownload() {
  if (props.download_with_annotation) {
    const {
      download_pdf,
    } = useDocumentTools(document_viewer_instance);
    download_pdf(true);
    return;
  }
  if (state.modified_attachment?.url)
    window.open(state.modified_attachment?.url, '_blank');
  else
    document_crud.itemDownload({ file: state.modified_attachment });
}

function onClose() {
  state.modified_attachment = null;
  emit('closeAttachment');
}
</script>

<template>
  <slot
    :is_attachment_supported="getSupportedAttachment(document)"
  />
  <HawkAttachmentViewer
    v-if="show_viewer"
    :key="state.modified_attachment?.url"
    :attachment="state.modified_attachment"
    :viewer_options="viewerOptions"
    @doc-viewer-instance="documentViewerInstance"
    @download="onDownload"
    @close="onClose"
  />
</template>
