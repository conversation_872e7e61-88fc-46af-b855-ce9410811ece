<script setup>
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import DOMPurify from 'dompurify';
import { debounce, isNil, keyBy } from 'lodash-es';
import { storeToRefs } from 'pinia';
import HawkHandsOnTable from '~/common/components/organisms/hawk-handsontable/hawk-handsontable.vue';
import { useMembers } from '~/common/composables/members.js';
import { stringToNumber } from '~/common/utils/common.utils.js';
import { useProjectManagementStore } from '~/project-management/store/pm.store';

const props = defineProps({
  activity: {
    type: Object,
    required: true,
  },
});

const emit = defineEmits(['close']);

dayjs.extend(isSameOrBefore);

const $t = inject('$t');
const $toast = inject('$toast');

const { getUserDetails } = useMembers();

const project_management_store = useProjectManagementStore();
const { $g, active_schedule, is_fullscreen } = storeToRefs(project_management_store);
const { create_bulk_tracking } = project_management_store;

const hot$ = ref(null);

const state = reactive({
  interval: 'daily',
  range: 'this_week',
  hot_data: [],
  hot_instance: null,
  activities: [],
  invalid_data: {
    activity_with_resource_but_empty_values: null,
    duplicate_activity_resource_pairs: [],
  },
  is_save_disabled: false,
  is_saving: false,
  force_update: 0,
});

const schedule_resources_map = computed(() => {
  return keyBy(active_schedule.value.resources, 'uid');
});

const interval_options = [
  ['daily', 'Daily'],
  ['weekly', 'Weekly'],
].map(item => ({
  value: item[0],
  label: $t(item[1]),
}));

const range_options = [
  ['this_week', 'This week', dayjs().startOf('week'), dayjs().endOf('week')],
  ['this_month', 'This month', dayjs().startOf('month'), dayjs().endOf('month')],
  ['last_7_days', 'Last 7 days', dayjs().subtract(7, 'days'), dayjs()],
  ['last_14_days', 'Last 14 days', dayjs().subtract(14, 'days'), dayjs()],
  ['last_30_days', 'Last 30 days', dayjs().subtract(30, 'days'), dayjs()],
].map(item => ({
  value: item[0],
  label: $t(item[1]),
  min_date: item[2],
  max_date: item[3],
}));

const dates = computed(() => {
  const current_range = range_options.find(option => option.value === state.range);
  let dates = getDatesBetween(current_range.min_date, current_range.max_date);

  if (state.interval === 'weekly') {
    dates = dates.filter(date => dayjs(date).day() === 1);
  }

  const dates_map = {};
  dates.forEach((date) => {
    const key = state.interval === 'weekly' ? `${date.format('YYYY-MM-DD')}_${date.add(6, 'day').format('YYYY-MM-DD')}` : `${date.format('YYYY-MM-DD')}_${date.format('YYYY-MM-DD')}`;
    const label = state.interval === 'weekly' ? `${dayjs(date).format('DD MMM YYYY')} to ${dayjs(date).add(6, 'day').format('DD MMM YYYY')}` : dayjs(date).format('DD MMMM YYYY');
    dates_map[key] = {
      label,
    };
  });
  return dates_map;
});

const hot_table_height = computed(() => {
  let calculated_height = (state.hot_data.length * 30) + 87;
  if (calculated_height > 550)
    calculated_height = 550;
  if (calculated_height < 200)
    calculated_height = 200;
  return `${calculated_height}px`;
});

const hot_columns = computed(() => {
  const dateColumns = Object.keys(dates.value).map(date => ({
    header: dates.value[date].label,
    columns: [
      {
        data: `${date}_work`,
        header: $t('Work'),
        type: 'numeric',
        width: 80,
        suffix: $t('units'),
        renderer: 'suffixPrefixRenderer',
      },
      {
        data: `${date}_duration`,
        header: $t('Duration'),
        type: 'numeric',
        width: 80,
        suffix: $t('hrs'),
        renderer: 'suffixPrefixRenderer',
      },
      {
        data: `${date}_items`,
        header: $t('Items'),
        type: 'numeric',
        width: 80,
        suffix: $t('pcs'),
        renderer: 'suffixPrefixRenderer',
      },
      {
        data: `${date}_cost`,
        header: $t('Cost'),
        type: 'numeric',
        width: 100,
        prefix: active_schedule.value.currency.symbol,
        renderer: 'suffixPrefixRenderer',
      },
    ],
  }));

  return [
    {
      data: 'id',
      header: $t('ID'),
      readOnly: true,
    },
    {
      data: 'activity',
      header: $t('Activity'),
      readOnly: true,
      wordWrap: false,
    },
    {
      data: 'resource',
      validator: 'default-validator',
      type: 'dropdown',
      source: [],
      header: $t('Assigned resource'),
      width: 230,
      wordWrap: false,
    },
    ...dateColumns.flatMap(dateCol => dateCol.columns),
  ];
});

const hot_nested_headers = computed(() => {
  const firstLevel = [
    $t('ID'),
    $t('Activity'),
    $t('Assigned resource'),
    ...Object.keys(dates.value).map(date => ({ label: dates.value[date].label, colspan: 4 })),
  ];

  const secondLevel = [
    '',
    '',
    '',
    ...Object.keys(dates.value).flatMap(() => [
      $t('Work'),
      $t('Duration'),
      $t('Items'),
      $t('Cost'),
    ]),
  ];

  return [firstLevel, secondLevel];
});

const hierarchy_breadcrumbs = computed(() => {
  const parents = [];
  $g.value.eachParent((task) => {
    parents.push({
      value: task.uid,
      label: task.text,
      uid: task.id,
      has_children: true,
      truncate_length: Math.min(task.text?.length, 20),
    });
  }, props.activity.id);
  return parents.reverse();
});

function getDatesBetween(min_date, max_date) {
  const dates = [];
  let current_date = min_date || dayjs(min_date);
  const end_date = max_date || dayjs(max_date);
  while (current_date.isSameOrBefore(end_date)) {
    dates.push(current_date);
    current_date = current_date.add(1, 'day');
  }
  return dates;
}

function memberResourceRenderer(instance, td, row, col, prop, value, cellProperties) {
  td.innerHTML = '';
  if (cellProperties.valid === false) {
    td.className += ' htInvalid';
  }
  td.className += ' htMiddle';
  const { members_details } = getUserDetails(schedule_resources_map.value[value]?.external_id);
  members_details?.forEach((user) => {
    if (user) {
      const display_name = user.name;

      const user_details = {
        bg_color: stringToNumber(user?.name),
        label: DOMPurify.sanitize(display_name.trim(), { ALLOWED_TAGS: [] }),
        id: user.uid,
        avatar: user?.display_picture ?? '',
        email: DOMPurify.sanitize(user?.email, { ALLOWED_TAGS: [] }),
      };

      const container = document.createElement('div');
      container.style.display = 'flex';
      container.classList = `gap-1`;
      container.style.alignItems = 'center';
      container.style.marginBottom = '4px';
      if (user_details.avatar) {
        const img = document.createElement('img');
        img.src = user?.display_picture ?? '';
        img.className = `rounded-full object-cover !text-xs !w-4 !h-4 flex justify-center`;
        img.style.width = '24px';
        img.style.height = '24px';
        container.appendChild(img);
      }
      else {
        const span = document.createElement('span');
        span.className = `rounded-full !text-xs !w-4 !h-4 flex justify-center`;
        span.style.width = '24px';
        span.style.height = '24px';
        span.style.backgroundColor = user_details?.bg_color;
        span.textContent = user_details.label?.substring(0, 1);
        span.style.color = 'white';
        span.style.alignContent = 'center';
        container.appendChild(span);
      }
      const name = document.createElement('span');
      name.textContent = user_details.label;

      container.appendChild(name);
      td.appendChild(container);
    }
  });
  return td;
}

function customResourceRenderer(instance, td, row, col, _prop, _value, cellProperties) {
  const hot_row_data = instance.getData()[row];
  td.textContent = schedule_resources_map.value[hot_row_data[col]]?.name;
  td.classList.add('htMiddle');

  if (cellProperties.valid === false) {
    td.className += ' htInvalid';
  }

  return td;
}

function afterBeginEditing(row, column) {
  const current_value = state.hot_instance.getDataAtCell(row, column);
  const resource = active_schedule.value.resources.find((resource) => {
    return resource.uid === current_value;
  });
  if (resource?.type === 'member')
    state.hot_instance.getActiveEditor()?.setValue(getUserDetails([resource.external_id]).members_details[0].name);
  else if (resource?.type === 'custom')
    state.hot_instance.getActiveEditor()?.setValue(resource.name);
}

const debouncedAfterChange = debounce((changes, source) => {
  if (source === 'CopyPaste.paste' || source !== 'edit')
    return;

  const updates = [];

  changes?.forEach?.((change) => {
    if ((change[1].endsWith('_duration') || change[1].endsWith('_items'))) {
      const rowData = state.hot_instance.getSourceDataAtRow(change[0]);
      updateCostBasedOnDurationOrItems(change[0], rowData.resource, change[3], change[1]);
    }
    if (change[1] === 'resource') {
      const value = change[3];
      const row = change[0];
      const old_value = change[2];

      const member = active_schedule.value.resources.find((resource) => {
        if (resource.type === 'member') {
          const member = getUserDetails([resource.external_id]).members_details[0];
          return member?.name === value || member?.name === value?.[0];
        }
        else {
          return resource.name === value || resource.name === value?.[0];
        }
      });

      const is_the_value_already_uid = active_schedule.value.resources.some((resource) => {
        return resource.uid === value || resource.uid === value?.[0];
      });

      if (!is_the_value_already_uid && member) {
        const colIndex = hot_columns.value.findIndex(column => column.data === 'resource');
        updates.push([row, colIndex, member.uid]);
      }
      else if (!value || value === '' || value === null) {
        if (old_value && old_value !== '') {
          clearDurationAndItemsForRow(row);
        }
      }
    }
  });

  if (updates.length > 0) {
    state.hot_instance.setDataAtCell(updates);
  }
}, 100);

function afterChange(changes, source) {
  debouncedAfterChange(changes, source);
}

function clearDurationAndItemsForRow(row) {
  const updates = [];

  Object.keys(dates.value).forEach((date) => {
    const durationColIndex = hot_columns.value.findIndex(column => column.data === `${date}_duration`);
    const itemsColIndex = hot_columns.value.findIndex(column => column.data === `${date}_items`);

    if (durationColIndex !== -1) {
      updates.push([row, durationColIndex, null]);
    }
    if (itemsColIndex !== -1) {
      updates.push([row, itemsColIndex, null]);
    }
  });

  if (updates.length > 0) {
    state.hot_instance.setDataAtCell(updates);
  }
}

function beforeKeyDown(event) {
  logger.log('beforeKeyDown', event);
}

function getAvailableResourcesForActivity(activity_id, current_row) {
  const task = $g.value.getTask(activity_id);
  const task_resources = task?.resources || [];

  // Get all rows with the same activity
  const all_data = state.hot_instance.getData();
  const used_resources = all_data
    .filter((row, idx) => row[0] === activity_id && idx !== current_row)
    .map(row => row[2])
    .filter(Boolean);

  // Return resources that are assigned to the task but not yet used in the table (excluding current row)
  return task_resources.filter(resource_uid => !used_resources.includes(resource_uid));
}

function cellsConfiguration(row, col) {
  const cellProperties = {};

  const column_header = hot_columns.value[col]?.data;

  if (!state.hot_instance)
    return cellProperties;

  const row_data = state.hot_instance.getData()[row];
  const resource_col_index = hot_columns.value.findIndex(column => column.data === 'resource');

  const task = $g.value.getTask(row_data[0]);
  const activity_uid = task?.uid;
  const is_activity_completed = active_schedule.value.activities?.[activity_uid]?.progress === 1;

  if (is_activity_completed || $g.value.config.types.milestone === task?.type) {
    cellProperties.readOnly = true;
    return cellProperties;
  }

  if (col === resource_col_index && row_data) {
    const available_resources = getAvailableResourcesForActivity(row_data[0], row);

    cellProperties.source = available_resources.map((resource_uid) => {
      const resource = schedule_resources_map.value[resource_uid];
      if (resource.type === 'member')
        return getUserDetails([resource.external_id]).members_details[0].name;
      else if (resource.type === 'custom')
        return resource.name;
      return null;
    });

    if (!cellProperties.source?.length) {
      cellProperties.readOnly = true;
    }

    if (row_data?.[resource_col_index]) {
      const resource = active_schedule.value.resources.find((resource) => {
        return resource.uid === row_data[resource_col_index];
      });

      if (resource?.type === 'member') {
        cellProperties.renderer = memberResourceRenderer;
      }
      else {
        cellProperties.renderer = customResourceRenderer;
      }
    }
  }

  const resource = active_schedule.value.resources.find((resource) => {
    return resource.uid === row_data?.[resource_col_index];
  });
  if (column_header.includes('duration')) {
    if (resource?.cost_type === 'per_hour')
      cellProperties.readOnly = false;
    else
      cellProperties.readOnly = true;
  }
  if (column_header.includes('items')) {
    if (resource?.cost_type === 'per_item')
      cellProperties.readOnly = false;
    else
      cellProperties.readOnly = true;
  }

  return cellProperties;
}

function beforeChange() {
  // For some reason, the members selection is failing if this empty beforeChange function is not called
}

function onHandsOnTableReady(hot_instance) {
  state.hot_instance = hot_instance;

  state.hot_instance.updateSettings({
    cells: cellsConfiguration,
  });

  state.hot_instance.render();
}

function generateInitialData() {
  state.activities = [];
  if ([$g.value.config.types.task, $g.value.config.types.milestone].includes(props.activity.type)) {
    state.activities = [props.activity];
  }
  else {
    state.activities = $g.value.getChildren(props.activity.id)
      .map(child => $g.value.getTask(child))
      .filter((child) => {
        return [$g.value.config.types.task, $g.value.config.types.milestone].includes(child.type);
      });
  }

  return state.activities.map((item) => {
    const baseData = {
      id: item.id,
      activity: item.text,
      resource: item.resource,
    };

    Object.keys(dates.value).forEach((date) => {
      baseData[`${date}_work`] = null;
      baseData[`${date}_cost`] = null;
      baseData[`${date}_duration`] = null;
      baseData[`${date}_items`] = null;
    });

    return baseData;
  });
}

function clearTableData() {
  if (!state.hot_instance) {
    state.hot_data = generateInitialData();
    return;
  }

  const currentData = state.hot_instance.getData();

  const clearedData = currentData.map((row) => {
    const newRow = [...row];

    for (let i = 3; i < newRow.length; i++) {
      newRow[i] = null;
    }

    return newRow;
  });

  state.hot_data = state.activities.map((item) => {
    const existingRow = clearedData.find(row => row[0] === item.id);

    const baseData = {
      id: item.id,
      activity: item.text,
      resource: existingRow ? existingRow[2] : item.resource,
    };

    Object.keys(dates.value).forEach((date) => {
      baseData[`${date}_work`] = null;
      baseData[`${date}_cost`] = null;
      baseData[`${date}_duration`] = null;
      baseData[`${date}_items`] = null;
    });

    return baseData;
  });
}

function afterGetRowHeader(_row, th) {
  th.classList.add('htMiddle');
}

function updateCostBasedOnDurationOrItems(row, resource_uid, duration_item_value, duration_item_key) {
  if (isNil(duration_item_value) || duration_item_value === '')
    return;
  const selected_resource_uid = resource_uid;
  const resource = active_schedule.value.resources.find(
    r => r.uid === selected_resource_uid,
  );
  if (resource) {
    const rate = resource.cost || 0;
    const value = Number(duration_item_value) || 0;
    const cost = rate * value;
    const dateKey = duration_item_key.replace(/_(duration|items)$/, '');
    const costKey = `${dateKey}_cost`;
    state.hot_instance.setDataAtRowProp(row, costKey, cost, 'auto');
  }
}

function beforeCellCopyInterceptor(_data, copiedValue, _dataRowIndex, _dataColIndex, _cellMeta, _row, col) {
  const resource_col_index = hot_columns.value.findIndex(column => column.data === 'resource');

  if (col !== resource_col_index) {
    return undefined;
  }

  if (copiedValue) {
    const resource = schedule_resources_map.value[copiedValue];
    if (resource) {
      if (resource.type === 'member') {
        const member_details = getUserDetails([resource.external_id]).members_details[0];
        return member_details?.name || copiedValue;
      }
      else if (resource.type === 'custom') {
        return resource.name;
      }
    }
  }
  return copiedValue;
}

function beforeCellPasteInterceptor(data, pastedValue, dataRowIndex, dataColIndex, cellMeta, cellData, row, col) {
  if (cellMeta.prop === 'resource') {
    if (pastedValue && typeof pastedValue === 'string') {
      const trimmed_value = pastedValue.trim();

      const resource = active_schedule.value.resources.find((res) => {
        if (res.type === 'member') {
          const member_details = getUserDetails([res.external_id]).members_details[0];
          return member_details?.name === trimmed_value;
        }
        else if (res.type === 'custom') {
          return res.name === trimmed_value;
        }
        return false;
      });

      if (resource) {
        const row_data = state.hot_instance.getData()[row];
        const task = $g.value.getTask(row_data?.[0]);

        // Check if resource is assigned to the task
        if (task?.resources?.includes(resource.uid)) {
          // Check if this activity-resource combination already exists
          const all_data = state.hot_instance.getData();
          const duplicate_exists = all_data.some((r, idx) =>
            idx !== row && r[0] === row_data[0] && r[2] === resource.uid,
          );

          if (!duplicate_exists) {
            data[dataRowIndex % data.length][dataColIndex % data[0].length] = resource.uid;
            if (!cellMeta.readOnly) {
              state.hot_instance.setDataAtCell(row, col, resource.uid);
            }
            return;
          }
        }
      }

      data[dataRowIndex % data.length][dataColIndex % data[0].length] = cellData;
    }
  }
  else if (cellMeta.prop.includes('_duration') || cellMeta.prop.includes('_items')) {
    const resource_col_index = hot_columns.value.findIndex(column => column.data === 'resource');
    const paste_resource_col_index = resource_col_index - col + dataColIndex;

    const pasting_resource = data[dataRowIndex % data.length][paste_resource_col_index % data[0].length];

    if (pasting_resource && dataRowIndex >= 0 && paste_resource_col_index >= 0 && paste_resource_col_index < data[0].length) {
      const resource = active_schedule.value.resources.find((res) => {
        return res.uid === pasting_resource;
      });

      if (resource) {
        const is_duration_cell = cellMeta.prop.includes('_duration');
        const is_items_cell = cellMeta.prop.includes('_items');

        cellMeta.readOnly = false;

        if ((is_duration_cell && resource.cost_type === 'per_hour')
          || (is_items_cell && resource.cost_type === 'per_item')) {
          data[dataRowIndex % data.length][dataColIndex % data[0].length] = pastedValue;
        }
        else {
          data[dataRowIndex % data.length][dataColIndex % data[0].length] = cellData;
        }
      }
      else {
        data[dataRowIndex % data.length][dataColIndex % data[0].length] = cellData;
      }
    }

    const rowData = state.hot_instance.getSourceDataAtRow(row);
    updateCostBasedOnDurationOrItems(row, pasting_resource || rowData.resource, pastedValue, cellMeta.prop);
  }
}

function afterPaste(data, coords) {
  console.log('🆘 ~ data:', data);
  // Validate and remove duplicate activity-resource pairs after paste
  const resource_col_index = hot_columns.value.findIndex(column => column.data === 'resource');

  if (coords.some(coord => coord.startCol <= resource_col_index && coord.endCol >= resource_col_index)) {
    const activity_resource_map = new Map();
    const duplicates_to_clear = [];

    const all_data = state.hot_instance.getData();

    all_data.forEach((row_data, row_idx) => {
      if (row_data[0] && row_data[resource_col_index]) {
        const key = `${row_data[0]}_${row_data[resource_col_index]}`;

        if (activity_resource_map.has(key)) {
          duplicates_to_clear.push([row_idx, resource_col_index, null]);
        }
        else {
          activity_resource_map.set(key, row_idx);
        }
      }
    });

    if (duplicates_to_clear.length > 0) {
      state.hot_instance.setDataAtCell(duplicates_to_clear);

      $toast({
        title: $t('Duplicate resources removed'),
        text: $t('Some pasted resources were removed because they were already assigned to the same activity'),
        type: 'warning',
      });
    }
  }
}

function duplicateRow(row) {
  const row_data = state.hot_instance.getSourceDataAtRow(row);

  const new_row = {
    id: row_data.id,
    activity: row_data.activity,
    resource: null,
  };

  Object.keys(dates.value).forEach((date) => {
    new_row[`${date}_work`] = null;
    new_row[`${date}_cost`] = null;
    new_row[`${date}_duration`] = null;
    new_row[`${date}_items`] = null;
  });

  state.hot_data.splice(row + 1, 0, new_row);

  nextTick(() => {
    state.hot_instance.loadData(state.hot_data);
    state.hot_instance.render();
  });
}

function removeRow(row) {
  state.hot_data.splice(row, 1);

  nextTick(() => {
    state.hot_instance.loadData(state.hot_data);
    state.hot_instance.render();
  });
}

function getContextMenuItems(row) {
  return [
    {
      key: 'duplicate_row',
      name: $t('Duplicate row'),
      callback: () => duplicateRow(row),
    },
    {
      key: 'remove_row',
      name: $t('Remove row'),
      callback: () => removeRow(row),
    },
  ];
}

function validateDuplicateActivityResourcePairs() {
  const pairs = new Map();
  const duplicates = [];

  state.hot_data.forEach((row, index) => {
    if (row.resource && row.id) {
      const key = `${row.id}_${row.resource}`;
      if (pairs.has(key)) {
        const task = $g.value.getTask(row.id);
        const resource = schedule_resources_map.value[row.resource];
        const resource_name = resource?.type === 'member'
          ? getUserDetails([resource.external_id]).members_details[0]?.name
          : resource?.name;

        duplicates.push({
          activity: task?.text || row.activity,
          resource: resource_name,
        });
      }
      else {
        pairs.set(key, index);
      }
    }
  });

  return duplicates;
}

async function onSave() {
  state.is_saving = true;

  const duplicate_pairs = validateDuplicateActivityResourcePairs();
  if (duplicate_pairs.length > 0) {
    state.invalid_data.duplicate_activity_resource_pairs = duplicate_pairs;
    state.is_saving = false;
    return;
  }

  state.hot_data.forEach((row) => {
    const cloned_row = { ...row };
    delete cloned_row.id;
    delete cloned_row.activity;
    delete cloned_row.resource;
    if (row.resource && Object.values(cloned_row).every(value => value === null || value === '')) {
      state.invalid_data.activity_with_resource_but_empty_values = row.activity;
    }
  });
  if (state.invalid_data.activity_with_resource_but_empty_values) {
    state.is_saving = false;
    return;
  }

  try {
    const payload = state.hot_data.flatMap((row) => {
      const date_ranges = Object.keys(row)
        .filter(key => key.match(/^\d{4}-\d{2}-\d{2}_\d{4}-\d{2}-\d{2}_/))
        .map((key) => {
          const [start, end] = key.split('_').slice(0, 2);
          return { start, end };
        })
        .filter((value, index, self) =>
          index === self.findIndex(t => t.start === value.start && t.end === value.end),
        );

      return date_ranges.map(({ start, end }) => {
        const work = row[`${start}_${end}_work`];
        const cost = row[`${start}_${end}_cost`];
        const duration = row[`${start}_${end}_duration`];
        const items = row[`${start}_${end}_items`];

        if (work || cost || duration || items) {
          return {
            activity: $g.value.getTask(row.id).uid,
            resource: row.resource,
            start: dayjs(start).utc(true).startOf('day').toISOString(),
            end: dayjs(end).utc(true).endOf('day').toISOString(),
            work,
            cost,
            units: duration || items,
          };
        }
        return null;
      }).filter(item => item !== null);
    });
    await create_bulk_tracking(payload);
    $toast({
      title: payload.length > 1 ? $t('Activities tracked successfully') : $t('Activity tracked successfully'),
      text: $t('Your changes have been saved'),
      type: 'success',
    });
  }
  catch (error) {
    $toast({
      title: $t('Something went wrong'),
      text: $t('Please try again'),
      type: 'error',
    });
    logger.error(error);
  }
  finally {
    state.is_saving = false;
    emit('close');
  }
}

const debouncedValidation = debounce(() => {
  state.hot_instance?.validateCells?.((is_valid) => {
    state.is_save_disabled = !is_valid;
  });
}, 200);

watch([() => state.interval, () => state.range], () => {
  clearTableData();
  state.hot_instance = null;
  state.force_update++;
}, { deep: true });

watch(() => state.hot_data, () => {
  state.invalid_data.activity_with_resource_but_empty_values = null;
  state.invalid_data.duplicate_activity_resource_pairs = [];
  debouncedValidation();
}, { deep: true });

onMounted(() => {
  state.hot_data = generateInitialData();
});
</script>

<template>
  <HawkModalContainer
    :options="{ teleportTo: is_fullscreen ? '#pm-fullscreen-container' : 'body', escToClose: false }"
    content_class="w-[80vw]"
  >
    <HawkModalHeader @close="emit('close')">
      <template #title>
        <HawkBreadcrumbs
          :items="hierarchy_breadcrumbs"
          :show_active_color="false"
          :max_tags_to_display="3"
        />
        <div>
          <template v-if="state.activities.length > 1">
            {{ $t('Track the subtasks of') }} <span class="text-primary-600">{{ props.activity.text }} ({{ $g.getWBSCode(props.activity) }})</span>
          </template>
          <template v-else>
            {{ $t('Track') }} <span class="text-primary-600">{{ props.activity.text }} ({{ $g.getWBSCode(props.activity) }})</span>
          </template>
        </div>
      </template>
    </HawkModalHeader>
    <HawkModalContent>
      <div class="flex justify-between items-center mb-6">
        <div class="text-sm font-normal text-gray-900">
          {{ $t('Track work done, amount spent for an activity, or record for a particular resource.') }}
        </div>
        <div class="flex items-center gap-2">
          <HawkMenu
            position="fixed"
            :items="range_options"
            @select="state.range = $event.value"
          >
            <template #trigger="{ open }">
              <div class="flex items-center gap-1">
                <span class="text-xs font-normal text-gray-500">
                  {{ $t('Range') }}:
                </span>
                <span class="text-xs font-medium text-gray-900">
                  {{ range_options.find((option) => option.value === state.range)?.label }}
                </span>
                <IconHawkChevronUp v-if="open" />
                <IconHawkChevronDown v-else />
              </div>
            </template>
          </HawkMenu>
          <HawkMenu
            position="fixed"
            :items="interval_options"
            @select="state.interval = $event.value"
          >
            <template #trigger="{ open }">
              <div class="flex items-center gap-1">
                <span class="text-xs font-normal text-gray-500">
                  {{ $t('Interval') }}:
                </span>
                <span class="text-xs font-medium text-gray-900">
                  {{ interval_options.find((option) => option.value === state.interval)?.label }}
                </span>
                <IconHawkChevronUp v-if="open" />
                <IconHawkChevronDown v-else />
              </div>
            </template>
          </HawkMenu>
        </div>
      </div>
      <div v-if="state.hot_data.length" :style="{ height: hot_table_height }">
        <div v-if="state.hot_instance === null" class="absolute top-0 left-0 z-10 flex items-center justify-center w-full h-full">
          <HawkLoader class="z-10" />
        </div>
        <HawkHandsOnTable
          :key="state.force_update"
          ref="hot$"
          :hot-settings="{
            afterChange,
            beforeChange,
            beforeKeyDown,
            afterBeginEditing,
            afterGetRowHeader,
            afterPaste,
            nestedRows: false,
            autoRowSize: true,
            rowHeights: '30px',
            bindRowsWithHeaders: true,
            nestedHeaders: hot_nested_headers,
            cells: cellsConfiguration,
            fixedColumnsStart: 3,
            className: 'htMiddle',
            contextMenu: {
              callback: (key, selection, clickEvent) => {
                const row = selection[0].start.row;
                const items = getContextMenuItems(row);
                const item = items.find(i => i.key === key);
                if (item) {
                  item.callback();
                }
              },
              items: {
                duplicate_row: {
                  name: $t('Duplicate row'),
                },
                remove_row: {
                  name: $t('Remove row'),
                },
              },
            },
            dropdownMenu: false,
            columnSorting: false,
            headerClassName: 'htCenter',
          }"
          :right-click-menu="{}"
          :data="state.hot_data"
          :columns="hot_columns"
          :columns-menu="{ items: {} }"
          :before-cell-paste-interceptor="beforeCellPasteInterceptor"
          :before-cell-copy-interceptor="beforeCellCopyInterceptor"
          :add-new-row-on-enter="false"
          class="pm-excel-modal"
          :height="hot_table_height"
          @ready="onHandsOnTableReady"
        />
      </div>
      <div v-if="state.invalid_data.activity_with_resource_but_empty_values" class="text-sm text-error-600 mt-3">
        {{ $t('The activity') }}
        <span class="font-semibold">{{ state.invalid_data.activity_with_resource_but_empty_values }}</span>
        {{ $t('has a resource selected but no values entered. Please enter at least one value or remove the resource.') }}
      </div>
      <div v-if="state.invalid_data.duplicate_activity_resource_pairs.length > 0" class="text-sm text-error-600 mt-3">
        {{ $t('The following activity-resource combinations appear multiple times. Each resource can only be assigned once per activity:') }}
        <ul class="list-disc list-inside mt-2">
          <li v-for="(duplicate, index) in state.invalid_data.duplicate_activity_resource_pairs" :key="index">
            <span class="font-semibold">{{ duplicate.activity }}</span> - <span class="font-semibold">{{ duplicate.resource }}</span>
          </li>
        </ul>
      </div>
    </HawkModalContent>
    <HawkModalFooter>
      <template #right>
        <div class="flex justify-end w-full col-span-full">
          <HawkButton
            type="outlined"
            class="mr-4"
            @click="emit('close')"
          >
            {{ $t('Cancel') }}
          </HawkButton>
          <HawkButton
            color="primary"
            :loading="state.is_saving"
            :disabled="state.is_save_disabled"
            @click="onSave"
          >
            {{ $t('Save') }}
          </HawkButton>
        </div>
      </template>
    </HawkModalFooter>
  </HawkModalContainer>
</template>

<style lang="scss">
.pm-excel-modal {
  .changeType {
    @apply hidden;
  }

  .handsontableInput {
    line-height: 29px;
  }

  th.afterHiddenColumn::before{
    content: '' !important;
  }

  .htCommentCell {
    @apply bg-[#FFBEBA];
  }
}
</style>
