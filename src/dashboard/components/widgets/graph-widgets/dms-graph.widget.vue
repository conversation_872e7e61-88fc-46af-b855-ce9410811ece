<script setup>
import { isEqual,cloneDeep } from 'lodash-es';
import { watch } from 'vue';
import { useModal } from 'vue-final-modal';
import DMSListModal from '~/dashboard/components/widgets/dms-widgets/dms-list-modal.vue';
import GraphComponent from '~/dashboard/components/widgets/graph-widgets/graph-component.vue';
import { useFamConstants } from '~/forms-as-module/composables/fam-constants.composable.js';

const props = defineProps({
  data: {
    type: Object,
  },
  id: {
    type: String,
  },
  // eslint-disable-next-line vue/prop-name-casing
  is_mobile_view: {
    type: Boolean,
    default: false,
  },
  activeSelectedRange: {
    type: Array,
    default: () => [],
  },
});
const dms_list_modal = useModal({
  component: DMSListModal,
});

const { getFormattedDate } = useFamConstants();

const $services = inject('$services');
const graph_data = ref(null);
const loading = ref(false);

const chart_config = computed(() => {
  return props.data?.data?.chart_config;
});
const chart_type = computed(() => {
  let type;
  switch (props.data?.data?.type) {
    case 'vertical_graph':
      type = chart_config.value?.chart_display_mode === 'Unstacked' ? 'mscolumn2d' : 'stackedcolumn2d';
      break;
    case 'horizontal_bar':
      type = chart_config.value?.chart_display_mode === 'Unstacked' ? 'msbar2d' : 'stackedbar2d';
      break;
    case 'donut':
      type = 'doughnut2d';
      break;
  }

  return type;
});

const x_axis_label = computed(() => {
  if (props.data.data.data_source === 'files')
    return props.data.data?.files_field?.label;
  else return props.data.data?.transmittals_field?.label;
});
const y_axis_label = computed(() => {
  if (props.data.data.data_source === 'files')
    return props.data.data?.files_value?.label;
  else return props.data.data?.transmittals_value?.label;
});

const group_key = computed(() => {
  if (props.data.data.data_source === 'files')
    return props.data.data?.files_breakdown_by?.label;
  else return props.data.data?.transmittals_breakdown_by?.label;
});
const graph_config = computed(() => ({
  chart_name: props.data.data.name,
  dimensions: {
    x: props.data.x,
    y: props.data.y,
    h: props.data.h,
  },
  renderAt: `chart-${props.id}`,
  type: chart_type.value || '',
  dataSource: {
    chart: {
      xAxisName: chart_config.value?.x_label || x_axis_label.value || '',
      yAxisName: chart_config.value?.y_label || y_axis_label.value || '',
      subCaption: chart_config.value?.description
        || props.data.data.subCaption || '',
    },
    dashboard_index: props.data.i,
    chart_type: props.data.data.type,
    ...graph_data.value,
  },
  events: {
    dataPlotClick: (e) => {
      window.getFiles = (query) => {
        const count = e?.data.value || 0;
        const popup_query = JSON.parse(atob(query));
        dms_list_modal.patchOptions({
          attrs: {
            id: props.id,
            name: `${props.data?.data?.name}`,
            count,
            data: props.data,
            component_type: 'files',
            popup_query,
            onClose() {
              dms_list_modal.close();
            },
          },
        });
        dms_list_modal.open();
      };
      window.getTransmittals = (query) => {
        const count = e?.data.value || 0;
        const popup_query = JSON.parse(atob(query));
        dms_list_modal.patchOptions({
          attrs: {
            id: props.id,
            name: `${props.data?.data?.name}`,
            count,
            data: props.data,
            component_type: 'trasmittals',
            popup_query,
            onClose() {
              dms_list_modal.close();
            },
          },
        });
        dms_list_modal.open();
      };
    },
  },

}));

async function getReports() {
  graph_data.value = null;
  loading.value = true;
  const payload = cloneDeep(props.data.data);
  if (payload?.filters?.filters) {
    payload.filters.filters = payload.filters?.filters?.map((f) => {
      return { ...f, value: getFormattedDate(f.operator_option) };
    });
  }
  try {
    const { data } = await $services.dms.get_graph({ body: payload });
    graph_data.value = data;

    loading.value = false;
  }
  catch {
    loading.value = false;
  }
}

if (props.id === 'preview') {
  watch(() => props.data, (new_val, old_val) => {
    if (new_val && !isEqual(new_val, old_val)) {
      setTimeout(() => {
        getReports();
      }, 250);
    }
  }, { immediate: true, deep: true });
}

if (props.id !== 'preview') {
  watch(() => props.data.data, (new_val, old_val) => {
    if (new_val && !isEqual(new_val, old_val)) {
      setTimeout(() => {
        getReports();
      }, 250);
    }
  }, { immediate: true, deep: true });
}

watch(() => props.activeSelectedRange, (new_val, old_val) => {
  if (!isEqual(new_val, old_val) && (props?.id !== 'preview')) {
    getReports();
  }
});
</script>

<template>
  <div class="w-full h-full">
    <div v-if="$slots['header-title'] || $slots['header-actions']" class="widget-header group">
      <slot name="header-title" />
      <slot name="header-actions" />
    </div>
    <hawk-loader v-if="loading" />
    <GraphComponent v-else-if="graph_config?.renderAt" :id="props.id" :configuration="graph_config" :chart_config="chart_config" :group_key="group_key" />
  </div>
</template>
