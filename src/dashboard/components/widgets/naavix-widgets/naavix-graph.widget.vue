<script setup>
import { nextTick, onMounted } from 'vue';
import { load_js_css_file } from '~/common/utils/load-script.util.js';
import { useDashboardHelpers } from '~/dashboard/composables/dashboard-helpers.composable';
import { useDashboardStore } from '~/dashboard/store/dashboard.store.js';

const props = defineProps({
  data: {
    type: Object,
  },
  id: {
    type: String,
  },
  // eslint-disable-next-line vue/prop-name-casing
  is_editing: {
    type: Boolean,
    default: false,
  },
  canModifyResource: {
    type: Boolean,
    default: false,
  },
});

const dashboard_store = useDashboardStore();

const { deleteWidget } = useDashboardHelpers();

const $services = inject('$services');

const state = reactive({
  is_loading: false,
  data: null,
  layout: null,
});

const chartContainer = ref(null);

let resizeObserver;
if (typeof window !== 'undefined' && 'ResizeObserver' in window) {
  resizeObserver = new ResizeObserver(() => {
    if (props.data?.uid && state.data && state.layout) {
      Plotly.relayout(props.data.uid, {
        ...state.layout,
        autosize: true,
      });
    }
  });
}

async function updatePrintMap() {
  const graph_image = await Plotly.toImage(document.getElementById(props.data.uid), { format: 'png', width: 800, height: 400 });
  dashboard_store.update_print_map(props.id, {
    type: 'naavix_graph',
    renderAt: `chart-container-${props?.id}`,
    renderType: 'naavix-graph',
    width: '100%',
    height: '100%',
    dataFormat: 'json',
    dimensions: {
      x: props.data.x,
      y: props.data.y,
    },
    chart_name: props.data.data.name,
    dataSource: {
      graph_image,
      dashboard_index: props.data.i,
    },
  });
}

onMounted(async () => {
  state.is_loading = true;
  await load_js_css_file('https://cdn.plot.ly/plotly-cartesian-3.0.1.js', 'plotly', 'js');
  const response = await $services.ai.get_latest_artifact_data({
    agent_name: props.data.data.agent_name,
    artifact_id: props.data.data.artifact_id,
    query: { resource_id: props.data.data.resource_id, resource_type: props.data.data.resource_type },
  });
  state.is_loading = false;
  await nextTick();

  state.data = JSON.parse(response.data.chart_config).data;
  state.layout = JSON.parse(response.data.chart_config).layout;
  const final_layout = {
    ...state.layout,
    autosize: true,
  };

  if (!final_layout.xaxis)
    final_layout.xaxis = {};
  if (!final_layout.yaxis)
    final_layout.yaxis = {};
  final_layout.xaxis.automargin = true;
  final_layout.yaxis.automargin = true;

  if (document.getElementById(props.data.uid)) {
    await Plotly.newPlot(props.data.uid, state.data, final_layout);
    updatePrintMap();
  }
  await nextTick();
  if (chartContainer.value && resizeObserver) {
    resizeObserver.observe(chartContainer.value);
  }
});

onBeforeUnmount(() => {
  if (chartContainer.value && resizeObserver) {
    resizeObserver.unobserve(chartContainer.value);
  }
});
</script>

<template>
  <div>
    <div ref="chartContainer" class="h-[inherit]">
      <div v-if="$slots['header-title'] || $slots['header-actions']" class="widget-header group">
        <slot name="header-title" />
        <div v-if="props.is_editing && props.canModifyResource" class="flex items-center justify-around p-1">
          <p class="ml-2 cursor-pointer" @click="deleteWidget(props.data)">
            <icon-hawk-trash-three class="w-5 h-5" />
          </p>
        </div>
        <slot v-else name="header-actions" />
      </div>
      <HawkLoader v-if="state.is_loading" />
      <div v-else :id="props.data.uid" />
    </div>
  </div>
</template>
