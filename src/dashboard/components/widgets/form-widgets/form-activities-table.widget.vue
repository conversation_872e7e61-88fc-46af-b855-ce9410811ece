<script setup>
import { capitalize, cloneDeep, isEqual, toString } from 'lodash-es';
import { storeToRefs } from 'pinia';
import { watch } from 'vue';
import { useRoute } from 'vue-router';
import { useDashboardStore } from '~/dashboard/store/dashboard.store.js';
import { useDashboardFormsStore } from '~/dashboard/store/dashboard-forms.store.js';

import { useFamConstants } from '~/forms-as-module/composables/fam-constants.composable.js';

const props = defineProps({
  data: {
    type: Object,
  },
  id: {
    type: String,
  },
  // eslint-disable-next-line vue/prop-name-casing
  content_height: {
    type: Number,
  },
  activeSelectedRange: {
    type: Array,
    default: () => [],
  },
});

const dashboard_selected_range = inject('dashboardSelectedRange');

const route = useRoute();

const { parseRulesDateData } = useFamConstants();

const $services = inject('$services');
const $t = inject('$t');

const dashboard_store = useDashboardStore();
const dashboard_forms_store = useDashboardFormsStore();

const {
  forms_v2_filters,
} = storeToRefs(dashboard_forms_store);

const loading = ref(false);
const activities = ref([]);
const columns = ref([]);
const payload = ref(null);
const forceUpdate = ref(0);
const no_data = ref(false);
const prevent_watcher = ref(false);
const form_error = ref(false);
const handsontable_map = ref({});

async function getReports() {
  loading.value = true;
  payload.value = dashboard_forms_store.parse_forms_form_to_server_format(props.data.data);

  const forms_payload = cloneDeep(payload.value);

  const filters = forms_payload.filters.advanced_filter && Object.keys(forms_payload.filters.advanced_filter).length ? [forms_payload.filters.advanced_filter, ...cloneDeep(forms_v2_filters.value) || []] : cloneDeep(forms_v2_filters.value);

  forms_payload.filters.asset_uid = route.params.asset_id || null;

  forms_payload.filters = { ...forms_payload.filters, advanced_filter: filters };

  forms_payload.filters.advanced_filter = forms_payload.filters.advanced_filter.map((filter) => {
    filter.rules = parseRulesDateData(filter.rules);
    return filter;
  });
  try {
    const { data } = await $services.forms.get_graph({
      body: forms_payload,
    });
    if (data?.data?.length) {
      if (!props.data.data.transpose) {
        generateActivities(data.data);
      }
      else {
        props.data?.data?.breakdown.key && props.data.data.breakdown.key !== 'none'
          ? generateGroupedTransposeActivities(data.data)
          : generateUngroupedTransposeActivities(data.data);
      }

      no_data.value = false;
      forceUpdate.value++;
    }
    else {
      no_data.value = true;
      activities.value = [];
      columns.value = [];
    }

    form_error.value = null;
    loading.value = false;
  }
  catch (err) {
    form_error.value = err?.response?.status === 400 ? $t('Template is not published') : $t('Template not found');
    loading.value = false;
  }
}

const columns_widths_map = computed(() => props.data.data.columns_widths || {});

const height = computed(() => {
  return ((props.data.h || 22) * 20) - 44;
});

function generateUngroupedTransposeActivities(data) {
  const activity_array = [];
  const column_array = [];
  const field_name = props.data.data.field.label;
  const columnData = data.reduce((acc, item) => {
    // building column data
    const col_id = `column-${item[field_name]}`;
    column_array.push({
      // accessorFn: data => data[col_id],
      header: capitalize(item[field_name]),
      id: col_id,
      size: columns_widths_map.value[col_id] || 150,
      width: columns_widths_map.value[col_id] || 150,
      data: data => data[col_id],
      readOnly: true,
      text: capitalize(item[field_name]),
    });

    // building data required to build activities data
    Object.keys(item).forEach((key) => {
      if (!acc[key]) {
        acc[key] = [];
        data.forEach((newItem) => {
          acc[key].push(newItem[key]);
        });
      }
      else {
        data.forEach((newItem) => {
          acc[key].push(newItem[key]);
        });
      }
    });
    return acc;
  }, {});

  const values = Object.keys(columnData).find(key => key !== field_name);
  const result = columnData[field_name].reduce((acc, field, index) => {
    acc[`column-${field}`] = columnData[values][index];
    return acc;
  }, {});
  activity_array.push(result);
  activities.value = activity_array;
  columns.value = column_array;
}

function hotSettings() {
  return {
    rowHeaders: true,
    rowHeights: 26,
    viewPortRowRenderingOffset: 100,
    viewportColumnRenderingOffset: 40,
    dropdownMenu: false,
    contextMenu: false,
    filters: false,
    manualColumnResize: props.id === 'preview' || dashboard_store.is_editing_dashboard,
    manualRowMove: false,
    manualColumnMove: false,
    readOnly: true,
  };
}
const colHeaders = function (index) {
  return columns.value[index].text;
};
function generateGroupedTransposeActivities(data) {
  const field_name = props.data.data.field.label;
  const column_array = [{
    id: 'name',
    width: columns_widths_map.value.name || 400,
    size: columns_widths_map.value.name || 400,
    data: 'name',
    readOnly: true,
    text: props.data.data.breakdown.label,
    header: props.data.data.breakdown.label,
  }];

  const used_column_keys = [];

  Object.values(data).forEach((value) => {
    Object.entries(value).forEach(() => {
      if (!used_column_keys.includes(value[field_name])) {
        column_array.push({
          id: value[field_name].toLowerCase().replace(' ', '_'),
          width: columns_widths_map.value[field_name] || 150,
          size: columns_widths_map.value[field_name] || 150,
          data: value[field_name].toLowerCase().replace(' ', '_'),
          readOnly: true,
          text: value[field_name],
          header: value[field_name],
        });
        used_column_keys.push(value[field_name]);
      }
    });
  });

  const names = new Set();
  for (const entry of data) {
    Object.keys(entry).forEach((key) => {
      if (key !== field_name)
        names.add(key);
    });
  }
  // Initialize output data
  const outputData = [];
  names.forEach((name) => {
    const dataItem = { name };
    data.forEach((entry) => {
      const textField = entry[field_name];
      const value = entry[name];
      dataItem[textField.toLowerCase().replace(' ', '_')] = value;
    });
    outputData.push(dataItem);
  });

  activities.value = outputData;
  columns.value = column_array;
}

function generateActivities(data) {
  const activity_array = [];
  const column_array = [];

  const used_column_keys = [];

  Object.values(data).forEach((value) => {
    const current_item = {};
    for (const [nestedKey, nestedValue] of Object.entries(value)) {
      current_item[nestedKey] = nestedValue || '';
      // this check prevents multiple columns being created with same id
      if (!used_column_keys.includes(nestedKey)) {
        column_array.push({
          id: nestedKey,
          width: columns_widths_map.value[nestedKey] || 150,
          size: columns_widths_map.value[nestedKey] || 150,
          data: data => data[nestedKey],
          readOnly: true,
          text: nestedKey,
          header: nestedKey,
        });
        used_column_keys.push(nestedKey);
      }
    }
    activity_array.push(current_item);
  });

  activities.value = activity_array;
  columns.value = column_array;
}

function updatePrintMap() {
  dashboard_store.update_print_map(props.id, {
    type: props.data.data.type,
    renderAt: `chart-container-${props?.id}`,
    renderType: 'table',
    width: '100%',
    height: '100%',
    dashboard_selected_range:dashboard_store.get_global_date_range_filter_value(props.data),
    dataFormat: 'json',
    chart_name: props.data.data.name,
    dimensions: {
      x: props.data.x,
      y: props.data.y,
    },
    dataSource: {
      columns: columns.value,
      activities: activities.value,
      dataset: activities.value,
      is_transpose: props.data.data.transpose === true,
      dashboard_index: props.data.i,
      is_new_pivot_chart: props.data.data.chart === 'workflow_pivot_table',
    },
  });

  dashboard_store.update_new_print_map((props.data?.data?.name || 'untitled'), {
    widget_type: 'table',
    type: props.data?.data?.type,
    dataSource: parseNewPrintData(activities.value),
  });
}

function parseNewPrintData(data) {
  const accessor_keys = columns.value.map(c => c.data);
  const parsed_data = [];
  data.forEach((d) => {
    parsed_data.push(accessor_keys.map(k => toString(d[k])));
  });
  return parsed_data;
}

function columnResized(columns_widths) {
  // prevents the table from rerendering
  prevent_watcher.value = true;
  const columns_width_by_key = columns.value.reduce((acc, col, idx) => {
    acc[col.data] = { size: columns_widths[idx], id: col.data };
    return acc;
  }, {});

  dashboard_store.set_table_column_widths(
    props?.id,
    columns_width_by_key,
  );
}

watch(() => props.data.data, async (new_val, old_val) => {
  if (new_val && !isEqual(new_val, old_val)) {
    if (prevent_watcher.value) {
      prevent_watcher.value = false;
      return;
    }
    await getReports();
    if (props?.id !== 'preview')
      updatePrintMap();
  }
}, { immediate: true, deep: true });

watch(() => props.activeSelectedRange, async (new_val, old_val) => {
  if (!isEqual(new_val, old_val) && (props?.id !== 'preview')) {
    await getReports();
    updatePrintMap();
  }
});

watch(() => dashboard_store.is_editing_dashboard, async (new_val) => {
  if (handsontable_map.value[props.id]) {
    handsontable_map.value[props.id].updateSettings({
      manualColumnResize: new_val,
    });
  }
}, { immediate: true });
</script>

<template>
  <div>
    <div v-if="$slots['header-title'] || $slots['header-actions']" class="widget-header group">
      <slot name="header-title" />
      <slot name="header-actions" />
    </div>
    <div v-if="no_data" class="text-sm font-semiBold w-full" :class="dashboard_store.is_mobile_view ? 'h-[240px] grid place-items-center' : 'mt-8 flex justify-center'">
      {{ $t('No data present') }}
    </div>
    <hawk-loader v-if="loading" />
    <a v-else-if="activities?.length">
      <div class="w-full scrollbar" :style="{ height: `${content_height || height}px` }">
        <HawkHandsontable
          :apply-read-only-class="false"
          :data="activities"
          :columns="columns"
          :hot-settings="hotSettings()"
          :add-new-row-on-enter="false"
          :hot-table-id="id"
          :col-headers="colHeaders"
          :height="id !== 'preview' ? '100%' : '450px'"
          @ready="handsontable_map[id] = $event"
          @after-columns-resized="columnResized"
        />
      </div>
    </a>
    <div
      v-else-if="!loading && form_error"
      class="h-[calc(100%-80px)] pt-10 w-full flex items-center justify-center"
    >
      <div class="text-sm">
        {{ form_error }}
      </div>
    </div>
  </div>
</template>
