<script setup>
import { find, keyBy } from 'lodash-es';
import TableWrapperVue from '~/common/components/organisms/hawk-table/table.wrapper.vue';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import { getDuration } from '~/common/utils/date.util';
import JobsFilter from '~/jobs/components/jobs-filter.vue';

const { $t, $services, auth_store } = useCommonImports();

const TASK_STATUS = {
  FAILED: { label: 'Failed', color: 'red' },
  FINISHED: { label: 'Finished', color: 'green' },
  IN_PROGRESS: { label: 'In progress', color: 'blue' },
  PENDING: { label: 'Pending', color: 'orange' },
};
const INSTANCE_STATUS = {
  'pending': { label: 'Pending', color: 'yellow' },
  'running': { label: 'Running', color: 'green' },
  'shutting-down': { label: 'Shutting down', color: 'yellow' },
  'stopped': { label: 'Stopped', color: 'red' },
  'stopping': { label: 'Stopping', color: 'yellow' },
  'terminated': { label: 'Terminated', color: 'red' },
};
const COLUMNS = [
  { header: $t('Name'), accessorKey: 'task_name', id: 'task_name', cell: info => info.getValue() },
  { header: $t('Project'), accessorKey: 'project', id: 'project', cell: info => info.getValue() },
  { header: $t('Job status'), accessorKey: 'task_status', id: 'task_status', cell: info => info.getValue() },
  { header: $t('Instance type'), accessorKey: 'instance_type', id: 'instance_type', cell: info => info.getValue() },
  { header: $t('Instance status'), accessorKey: 'instance_status', id: 'instance_status', cell: info => info.getValue() },
  { header: $t('Last Updated'), accessorKey: 'updated_at', id: 'updated_at', cell: info => info.getValue() },
  { header: $t('IP address'), accessorKey: 'instance_ipv4', id: 'instance_ipv4', cell: info => info.getValue() || '-' },
  { header: $t('Duration'), accessorKey: 'duration', id: 'job_duration', cell: info => info.getValue() },
];

const DETAILS_VALUE = [
  { uid: 'user.uid', label: 'Initiated by', type: 'member' },
  { uid: 'created_at', label: 'Initiated on', type: 'date' },
  { uid: 'instance_type', label: 'Report', type: 'text' },
  { uid: 'instance_created_at', label: 'Created', type: 'date' },
  { uid: 'updated_at', label: 'updated', type: 'date' },
  { uid: 'LaunchTime', label: 'Instance launched', type: 'date' },
  { uid: 'instance_terminated_at', label: 'Instance terminated', type: 'date' },
];

const table_instance = ref(null);
const state = reactive({
  is_loading: false,
  selected_sort_type: '-created_at',
  search: '',
  data: [],
  resources: {},
  filters: {},
  pagination_config: { totalRows: 0, pageSize: 25, pageIndex: 1 },
});

const sorting_menu = computed(() => [
  {
    label: $t('Latest first'),
    uid: '-created_at',
    on_click: () => {
      state.selected_sort_type = '-created_at';
      state.pagination_config.pageIndex = 1;
      getDetails({ pagination_state: state.pagination_config });
    },
  },
  {
    label: $t('Oldest first'),
    uid: 'created_at',
    on_click: () => {
      state.selected_sort_type = 'created_at';
      state.pagination_config.pageIndex = 1;
      getDetails({ pagination_state: state.pagination_config });
    },
  },
]);

function getValues(details, key_value) {
  return key_value.split('.').reduce((acc, key) => acc?.[key], details);
}

async function getDetails({ pagination_state, signal }) {
  try {
    state.pagination_config = { ...pagination_state };
    state.is_loading = true;
    const query = {
      complete: true,
      page_number: pagination_state.pageIndex,
      page_size: pagination_state.pageSize,
      ...(state.search?.length ? { q: state.search } : {}),
      sort: state.selected_sort_type,
      ...state.filters,
    };

    const { data, headers } = await $services.jobs.get_jobs({ query, signal });
    state.data = data;
    state.pagination_config.totalRows = Number(headers['x-total-count']);

    const resource_ids = state.data?.map(jobs => jobs.resource_id);
    const { data: response } = await $services.jobs.get_jobs_resources({ body: { resource_ids } });
    state.resources = keyBy([...response.reports, ...response.pix4d], 'uid');

    state.is_loading = false;
  }
  catch (error) {
    logger.log('🚀 ~ getDetails ~ error:', error);
    state.is_loading = false;
  }
}

async function toggleExpanded(log_uid) {
  const index = state.data?.findIndex(log => log.uid === log_uid);
  state.data[index].is_expanded = !state.data[index].is_expanded;
}

async function onSearch(value) {
  state.data = [];
  table_instance.value.resetPagination();
  state.search = value;
  state.pagination_config = { pageSize: 25, pageIndex: 1 };
  await getDetails({ pagination_state: state.pagination_config });
}

async function onFilterApply({ filter, signal }) {
  state.data = [];
  state.filters = filter;
  await getDetails({ pagination_state: { pageIndex: 1, pageSize: 25 }, signal });
}

function getProjectPath(resource_id) {
  if (!state.resources?.[resource_id])
    return;
  const { organization, asset, group, project } = state?.resources?.[resource_id];
  const path = [organization, asset, group];
  if (project?.uid) {
    path.push(project);
  }
  return path;
}

function getReportDetailLink(job) {
  const base_url = getBaseUrlFromEnvironment();
  const { organization, group, project, process_uid } = state.resources[job.resource_id];
  const url = buildUrlByTaskType(job.task_name, { base_url, organization, group, project, process_uid });
  window.open(url, '_blank');
}

function getBaseUrlFromEnvironment() {
  const env = import.meta.env.VITE_APP_ENV;
  const envToBaseUrl = {
    'qa': 'https://app-old.qa-taskmapper.com',
    'development': 'https://app-old.dev-taskmapper.com',
    'production': 'https://app-old.taskmapper.com',
    'ril-production': 'http://app-old.sdprnel.com',
  };

  return envToBaseUrl[env] || '';
}

function buildUrlByTaskType(taskName, params) {
  const { base_url, organization, group, project, process_uid } = params;

  if (taskName === 'zipper' || taskName === 'mbtiler') {
    return `${base_url}/core/project-management/group/${group.uid}/project/${project.uid}/details/?organization=${organization.uid}`;
  }

  else if (taskName === 'pix4d') {
    return `${base_url}/process/${process_uid}/details/?organization=${organization.uid}`;
  }
  return '';
}

onMounted(async () => {
  if (auth_store.is_internal_user)
    await getDetails({ pagination_state: state.pagination_config });
});
</script>

<template>
  <div>
    <HawkPageHeader :title="`${$t('Jobs')}`" />
    <HawkIllustrations v-if="!auth_store.is_internal_user" type="no-permission" />
    <div v-else class="mx-4">
      <div class="flex gap-3 items-center justify-between flex-shrink-0 mb-4">
        <div class="flex gap-3 items-center">
          <HawkSearchInput v-model="state.search" :placeholder="$t('Search')" :debounce_time="500" @update:model-value="onSearch" />
          <JobsFilter :class="{ 'opacity-30 pointer-events-none': state.is_loading }" @apply="onFilterApply($event)" />
        </div>
        <HawkMenu
          :has_bordered_trigger="true"
          :items="sorting_menu"
          additional_dropdown_classes="w-52"
          @select="(e) => e.on_click()"
        >
          <template #trigger="{ open }">
            <div
              class="bg-white rounded-lg shadow-xs text-sm h-9 flex items-center justify-between px-3 w-52"
            >
              <div>
                <span class="text-gray-500 mr-1">{{ $t('Sort by') }}:</span>
                <span class="text-gray-900 font-medium mr-3">
                  {{ find(sorting_menu, (o) => { return o.uid === state.selected_sort_type; })?.label }}
                </span>
              </div>
              <IconHawkChevronUp v-if="open" class="text-lg" />
              <IconHawkChevronDown v-else class="text-lg" />
            </div>
          </template>
        </HawkMenu>
      </div>
      <HawkLoader v-if="state.is_loading" />
      <HawkIllustrations v-else-if="!state.data?.length" type="no-data" />
      <TableWrapperVue v-else container_class="border-0 jobs-table">
        <HawkTable
          :data="state.data"
          :columns="COLUMNS"
          is_gapless
          :manual_pagination="true"
          :show_menu_header="false"
          :pagination_config="state.pagination_config"
          @pagination="getDetails($event)"
          @table-instance="table_instance = $event"
        >
          <template v-for="row in state.data.filter(log => log.is_expanded)" :key="row.uid" #[`row_info_${row.uid}`]>
            <div class="pb-4 pt-3 grid test gap-5">
              <div v-for="item in DETAILS_VALUE" :key="item.uid">
                <div class="text-xs uppercase text-gray-600">
                  {{ item.label }}
                </div>
                <HawkMembers v-if="item.type === 'member'" :members="getValues(row, item.uid)" type="badge" />
                <div v-else-if="item.type === 'date'" class="text-sm font-medium">
                  {{ getValues(row, item.uid) ? $date(getValues(row, item.uid), 'L_DATETIME_MED') : '-' }}
                </div>
                <div v-else class="text-sm font-medium">
                  {{ getValues(row, item.uid) ? getValues(row, item.uid) : '-' }}
                </div>
              </div>
            </div>
          </template>
          <template #task_name="task_name">
            <hawk-button icon type="plain" class="ml-[-15px]" @click="toggleExpanded(task_name.data.row.original.uid)">
              <IconHawkChevronRight class="text-white transition-transform" :class="{ 'rotate-90 !visible': task_name.data.row.original.is_expanded }" />
            </hawk-button>
            <p class="whitespace-nowrap font-medium">
              {{ task_name.data.getValue() }}
            </p>
          </template>
          <template #project="project">
            <div v-if="getProjectPath(project.data.row.original?.resource_id)?.length" class="flex flex-wrap items-center">
              <div v-for="(item, index) in getProjectPath(project.data.row.original?.resource_id)" :key="item" class="flex items-center">
                {{ item?.name }}
                <IconHawkChevronRight v-if="index + 1 !== getProjectPath(project.data.row.original?.resource_id)?.length" class="size-4 mx-1 flex-shrink-0" />
              </div>
              <IconHawkShareFour class="invisible group-hover:visible size-4 ml-2 text-blue-700" @click="getReportDetailLink(project.data.row.original)" />
            </div>
            <div v-else>
              -
            </div>
          </template>
          <template #task_status="task_status">
            <HawkBadge :color="TASK_STATUS[task_status?.data?.getValue()]?.color">
              {{ TASK_STATUS[task_status?.data?.getValue()]?.label }}
            </HawkBadge>
          </template>
          <template #instance_status="instance_status">
            <HawkBadge :color="INSTANCE_STATUS[instance_status?.data?.getValue()]?.color">
              {{ INSTANCE_STATUS[instance_status?.data?.getValue()]?.label }}
            </HawkBadge>
          </template>
          <template #updated_at="updated_at">
            {{ $date(updated_at.data.getValue(), 'L_DATETIME_MED') }}
          </template>
          <template #job_duration="job_duration">
            {{ getDuration(job_duration.data.getValue(), 'seconds') }}
          </template>
        </HawkTable>
      </TableWrapperVue>
    </div>
  </div>
</template>

<style lang="scss">
.jobs-table tr .table-cell {
  padding-left: 40px !important;
}

.test {
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
}
</style>
