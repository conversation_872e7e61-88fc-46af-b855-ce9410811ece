<script setup>
import { cloneDeep } from 'lodash-es';
import { VueFinalModal } from 'vue-final-modal';
import { useCommonImports } from '~/common/composables/common-imports.composable.js';
import InventoryCustomFieldInput from '~/inventory/components/inventory-custom-fields/inventory-custom-field-input.vue';
import { useCustomFields } from '~/inventory/composables/inventory-custom-fields.composable.js';
import { useInventoryStore } from '~/inventory/store/inventory.store.js';

const props = defineProps({
  updateDetails: {
    type: Object,
    default: () => ({}),
  },
});
const { $t, auth_store, route } = useCommonImports();
const { map_custom_fields } = useCustomFields();
const inventory_store = useInventoryStore();
const custom_fields_config = ref([]);

const state = reactive({
  open_popup: false,
});

const transaction_details = computed(() => inventory_store.transaction_details);

const get_adjustment_custom_fields = computed(() =>
  inventory_store.get_custom_fields({ uid: transaction_details.value.workflow }, true).filter(cf => cf.attached_to.find(a => a.uid === transaction_details.value.workflow)?.properties?.edit_after_publish),
);
const form$ = ref(null);
const form = ref({});
watch(get_adjustment_custom_fields, async () => {
  await map_custom_fields(custom_fields_config, get_adjustment_custom_fields.value, form.value.custom_fields);
  form.value.custom_fields = custom_fields_config.value.map(f => ({
    uid: f.uid,
    value: f.value,
  }));
  await form$.value.load(form.value, true);
});
async function save() {
  try {
    const form_data = cloneDeep(form$.value.requestData);
    form_data.custom_fields = form_data?.custom_fields?.map((custom_field) => {
      return {
        created_at: new Date(),
        created_by: auth_store?.logged_in_user_details?.user_id,
        ...custom_field,
      };
    }) || [];
    await inventory_store.update_adjustment({
      id: transaction_details.value.uid,
      body: { adjustment: form_data },
      query: { asset: route.params.asset_id },
    });
    await props.updateDetails(true, false);
    state.open_popup = false;
  }
  catch (error) {
    logger.error('Error saving custom fields:', error);
  }
}

async function onFormMounted() {
  form.value.custom_fields = custom_fields_config.value.map(f => ({
    uid: f.uid,
    value: f.value,
  }));
  await form$.value.load(form.value, true);
}

onMounted(async () => {
  // if (transaction_details.value.uid) {
  await map_custom_fields(custom_fields_config, get_adjustment_custom_fields.value, transaction_details.value.custom_fields);

  // }
});
</script>

<template>
  <HawkButton v-if="custom_fields_config?.length" v-tippy="{ content: $t('Edit custom fields') }" icon type="text" @click="state.open_popup = !state.open_popup">
    <IconHawkEditThree />
  </HawkButton>
  <VueFinalModal
    v-model="state.open_popup"
    class="flex justify-center items-center"
    content-class="bg-white border space-t-2 rounded-xl w-[700px] max-w-4xl border-0"
    overlay-transition="vfm-fade"
    content-transition="vfm-fade"
  >
    <HawkModalHeader @close="state.open_popup = !state.open_popup">
      <template #title>
        {{ $t('Edit custom fields') }}
      </template>
    </HawkModalHeader>
    <Vueform
      ref="form$"
      v-model="form"
      size="sm"
      :display-errors="false"
      :columns="{
        default: { container: 12, label: 4, wrapper: 12 },
        sm: { container: 12, label: 4, wrapper: 12 },
        md: { container: 12, label: 4, wrapper: 12 },
      }"
      :format-load="(data) => data"
      :endpoint="save"
      @mounted="onFormMounted"
    >
      <div class="col-span-12">
        <HawkModalContent :is_scroll="true">
          <InventoryCustomFieldInput
            :key="`EAP-InventoryCustomFieldInput${custom_fields_config?.length}`"
            :options="{
              name: 'custom_fields',
            }"
            :data="custom_fields_config"
            :can_remove="false"
            :can_add_new_field="false"
            :input_size="6"
            :label_size="6"
            :attached_to_uid="transaction_details.workflow"
            attached_to="item_workflow"
          />
        </HawkModalContent>
        <HawkModalFooter>
          <template #right>
            <div class="flex gap-4 justify-end">
              <HawkButton type="outlined" @click="state.open_popup = !state.open_popup">
                {{ $t('Close') }}
              </HawkButton>
              <ButtonElement button-class="w-full bg-blue-600" name="submit" :submits="true">
                {{ $t('Save') }}
              </ButtonElement>
            </div>
          </template>
        </HawkModalFooter>
      </div>
    </Vueform>
  </VueFinalModal>
</template>
