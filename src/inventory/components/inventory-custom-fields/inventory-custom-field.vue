<script setup>
import dayjs from 'dayjs';
import DOMPurify from 'dompurify';
import parser from 'ua-parser-js';
import { useCommonImports } from '~/common/composables/common-imports.composable';

const props = defineProps({
  field: {
    type: Object,
    default: null,
  },
  value: {
    default: null,
  },
  signatureUrlKey: {
    default: 'url',
  },
});
const { $number } = useCommonImports();
const info = computed(() => {
  const meta = props.value?.meta || {};
  return meta?.city ? { Location: `${meta?.city}, ${meta?.region}`, Device: `${getDeviceInfo(meta.user_agent)}` } : null;
});

function formatted_value() {
  let result = '';
  if (!props.value)
    return '-';

  switch (props.field?.type) {
    case 'checkboxes':
      result = props.value.join(', ') || '-';
      break;
    case 'member':
      result = props.value;
      break;
    case 'members':
      result = props.value;
      break;
    case 'date':
      result = dayjs(props.value).format('D MMMM, YYYY');
      break;
    case 'number':
      result = $number(props.value);
      break;
    default:
      result = props.value;
  }
  return result;
}

function goToLink(url) {
  window.open(url, '_blank');
}

function getInfoHtml(info) {
  const html_string = Object.entries(info || {}).map(([key, value]) => {
    return `<div><b>${key}</b>: ${value}</div>`;
  }).join(' ');
  return DOMPurify.sanitize(html_string);
}

function getDeviceInfo(user_agent) {
  if (user_agent) {
    let string = '';
    const result = parser(user_agent);
    const device_os = result?.device?.model || result?.os?.name;
    string += result?.browser?.name ? `${result?.browser?.name}` : '';
    string += device_os ? `, ${device_os}` : '';
    return string;
  }
  return '';
}
</script>

<template>
  <div>
    <template v-if="field?.type === 'signature'">
      <div v-if="value">
        <img v-if="value" class="max-h-[40px] cursor-pointer border rounded-lg border-gray-300" :src="value?.[signatureUrlKey] ? value[signatureUrlKey] : value" alt="">
        <div v-if="value?.meta" class="flex gap-2 items-center mt-2 flex-wrap">
          <div class="flex items-center">
            {{ $t('Signed by') }}:
            <HawkMembers :members="value?.meta.owner?.uid || value?.meta.owner" type="badge" class="ml-2" />
          </div>
          <div class="flex items-center">
            {{ $t('on') }}
            <HawkText class="ml-2" :content="$date(value?.meta.created_at, 'L_DATETIME_MED')" :length="30" />
          </div>
          <div v-if="info" v-tippy="{ content: getInfoHtml(info), allowHTML: true, placement: 'bottom' }" class="pointer-events-auto">
            <IconHawkInfoCircle class="ml-1 w-4 h-4 mt-0.5" />
          </div>
        </div>
      </div>
      <div v-else>
        -
      </div>
    </template>
    <template v-else-if="field?.type === 'member'">
      <HawkMembers :members="value" type="label" :has_avatar="false" />
    </template>
    <template v-else-if="field?.type === 'members'">
      <HawkMembers :members="value" type="badge" />
    </template>
    <template v-else-if="field?.type === 'url'">
      <div class="cursor-pointer break-all hover:text-blue-600 hover:underline" @click="goToLink(DOMPurify.sanitize(value))">
        {{ value }}
      </div>
    </template>
    <template v-else-if="field?.type === 'files'">
      <div v-if="value?.length" class="mt-2 max-w-[400px]">
        <HawkAttachmentsGrid
          :items="value"
          :can_delete="false"
          :show_delete="false"
          :image_dimensions="[90, 90]"
          @click.stop=""
        />
      </div>
      <div v-else>
        -
      </div>
    </template>
    <template v-else-if="field?.type">
      {{ formatted_value() }}
    </template>
  </div>
</template>
