#!/bin/bash

set -e

echo "# -------------------------- [[ Running build.sh ]] -------------------------- #"
echo "# Site name: $SITE_NAME"
echo "# Site url: $URL"
echo "# Commit ref: $COMMIT_REF"

declare -A array

# DEVELOPMENT
array["app-dev-taskmapper"]="development"
array["mapsnapshot-dev-taskmapper"]="widget-mapsnapshot-development"
array["dashboard-dev-taskmapper"]="widget-dashboard-development"

# QA
array["app-qa-taskmapper"]="qa"
array["mapsnapshot-qa-taskmapper"]="widget-mapsnapshot-qa"
array["dashboard-qa-taskmapper"]="widget-dashboard-qa"

# PRODUCTION
array["app-taskmapper"]="production"
array["mapsnapshot-taskmapper"]="widget-mapsnapshot-production"
array["dashboard-taskmapper"]="widget-dashboard-production"

# RIL PRODUCTION
array["hawk-ui-ril-production"]="ril"
array["hawk-ui-terra-widget-ril"]="widget-mapsnapshot-ril"
array["hawk-ui-dashboard-widget-ril"]="widget-dashboard-ril"

# RIL v2
array["app-sdprnel"]="ril"
array["mapsnapshot-sdprnel"]="widget-mapsnapshot-ril"
array["dashboard-sdprnel"]="widget-dashboard-ril"

SITE_MODE=${array["$SITE_NAME"]}
if [ ! -z "$SITE_MODE" ]; then
    # Extract app version from package.json and fall back to current date in YYYY-MM-DD format if not found
    if [ -f package.json ]; then
        APP_VERSION=$(node -p "require('./package.json').version")
    else
        APP_VERSION=$(date +%Y-%m-%d)
    fi
    echo "# App version: $APP_VERSION"
    echo "# Building for site mode: $SITE_MODE"
    echo "# ---------------------------------------------------------------------------- #"

    VITE_COMMIT_REF=$COMMIT_REF pnpm run build --mode $SITE_MODE
    sed "s|{{ORIGIN}}|$URL|g" headers.template > dist/_headers

    # Only run datadog sourcemaps upload for specific environments
    if [ "$SITE_NAME" = "app-taskmapper" ] || [ "$SITE_NAME" = "app-qa-taskmapper" ] || [ "$SITE_NAME" = "app-dev-taskmapper" ]; then
        npx @datadog/datadog-ci sourcemaps upload ./dist --minified-path-prefix / --service=taskmapper --release-version=$APP_VERSION --disable-git
    fi
fi
