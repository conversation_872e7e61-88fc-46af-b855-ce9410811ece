# ----------------------------- DASHBOARD WIDGET ----------------------------- #

[context."release/dashboard/development"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."release/dashboard/qa"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."release/dashboard/production"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."release/dashboard/ril"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."widget/dashboard-ril"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."widget/v2/dashboard-ril"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

# ------------------------------- TERRA WIDGET ------------------------------- #

[context."release/mapsnapshot/development"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."release/mapsnapshot/qa"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."release/mapsnapshot/production"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."release/mapsnapshot/ril"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."widget/terra-ril"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."widget/v2/terra-ril"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

# --------------------------------- MAIN APP --------------------------------- #

[context."release/development"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."release/qa"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."release/pre-production"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."release/production"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."release/ril-production"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

[context."release/v2/ril-production"]
command = "chmod +x build.sh && ./build.sh"
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

# --------------------------------- FALLBACK --------------------------------- #

[build]
environment = { NODE_OPTIONS = "--max-old-space-size=12288", SECRETS_SCAN_SMART_DETECTION_ENABLED = "false" }

# ----------------------------------- NOTES ---------------------------------- #

# SENTRY_AUTH_TOKEN, DATADOG_API_KEY, DATADOG_APP_KEY, and CSP-related VARIABLES are to be declared on Netlify UI
