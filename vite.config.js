import dns from 'node:dns';
import path from 'node:path';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import Vue from '@vitejs/plugin-vue';
import vueform from '@vueform/vueform/vite';
import { visualizer } from 'rollup-plugin-visualizer';
import AutoImport from 'unplugin-auto-import/vite';
import { FileSystemIconLoader } from 'unplugin-icons/loaders';
import IconsResolver from 'unplugin-icons/resolver';
import Icons from 'unplugin-icons/vite';
import Components from 'unplugin-vue-components/vite';
import { defineConfig } from 'vite';
import Inspect from 'vite-plugin-inspect';
import Inspector from 'vite-plugin-vue-inspector';
import Layouts from 'vite-plugin-vue-layouts';
import { version } from './package.json';

dns.setDefaultResultOrder('verbatim');

const timestamp = new Date().getTime();
const enabledCollections = ['mdi', 'ic', 'vscode-icons'];

function getEnvDir(mode) {
  if (mode.includes('widget-dashboard'))
    return './widgets/dashboard/env';
  else if (mode.includes('widget-mapsnapshot'))
    return './widgets/mapsnapshot/env';
  return './env';
}

function getEntryPoint(mode) {
  if (mode.includes('widget-dashboard'))
    return '/widgets/dashboard/main.widget-dashboard.js';
  else if (mode.includes('widget-mapsnapshot'))
    return '/widgets/mapsnapshot/main.widget-mapsnapshot.js';
  return '/src/main.js';
}

export default defineConfig(({ mode }) => ({
  envDir: getEnvDir(mode),

  css: {
    preprocessorOptions: {
      scss: {
        silenceDeprecations: ['legacy-js-api'],
      },
    },
  },
  resolve: {
    alias: {
      '~/': `${path.resolve(__dirname, 'src')}/`,
    },
  },

  build: {
    sourcemap: true,
    rollupOptions: {
      output: {
        entryFileNames: `[name].${timestamp}.js`,
        chunkFileNames: `[name].${timestamp}.js`,
        assetFileNames: `[name].${timestamp}.[ext]`,
        manualChunks(id) {
          if (id.includes('~icons/hawk'))
            return 'icons';
        },
      },
    },
  },

  plugins: [
    {
      name: 'html-transform',
      transformIndexHtml: {
        order: 'pre',
        handler() {
          return [{
            tag: 'script',
            attrs: { type: 'module', src: getEntryPoint(mode) },
            injectTo: 'body',
          }];
        },
      },
    },

    visualizer({
      template: 'treemap', // or sunburst
      open: false,
      gzipSize: true,
      filename: 'analysis.html',
    }),

    Vue(),

    sentryVitePlugin({
      // eslint-disable-next-line node/prefer-global/process
      authToken: process.env.SENTRY_AUTH_TOKEN,
      org: 'sensehawk-sentry',
      project: 'taskmapper-frontend',
      release: {
        package: mode,
        version,
      },
      bundleSizeOptimizations: {
        excludeDebugStatements: true,
        // Only relevant if `browserTracingIntegration` is added
        excludePerformanceMonitoring: true,
        // Only relevant if `replayIntegration` is added
        excludeReplayIframe: true,
        excludeReplayShadowDom: true,
        excludeReplayWorker: true,
      },
    }),

    vueform(),

    // https://github.com/JohnCampionJr/vite-plugin-vue-layouts
    Layouts({
      layoutsDirs: 'src/common/layouts',
    }),

    // https://github.com/antfu/unplugin-auto-import
    AutoImport({
      imports: ['vue', 'vue-router', 'vue/macros', '@vueuse/core'],
      resolvers: [
        IconsResolver({
          componentPrefix: 'icon',
          enabledCollections,
          customCollections: ['hawk', 'illustration', 'checkbox', 'integrations'],
        }),
      ],
      dts: false,
    }),

    // https://github.com/antfu/unplugin-vue-components
    Components({
      // relative paths to the directory to search for components
      dirs: ['src/**/components', 'static/icons'],

      // allow auto load components under `./src/components/`
      extensions: ['vue'],

      // search for subdirectories
      deep: true,

      dts: false,

      // allow auto import and register components
      include: [/\.vue$/, /\.vue\?vue/],

      // custom resolvers
      resolvers: [
        // auto import icons
        // https://github.com/antfu/unplugin-icons
        IconsResolver({
          prefix: 'icon',
          enabledCollections,
          customCollections: ['hawk', 'illustration', 'checkbox', 'integrations'],
        }),
      ],
    }),

    // https://github.com/antfu/unplugin-icons
    Icons({
      autoInstall: true,
      customCollections: {
        // files under `./assets/icons` with `.svg` extension will be loaded as it's file name
        // Use as: icon-hawk-<icon-file-name-without-extension>
        hawk: FileSystemIconLoader('./assets/icons', (svg) => {
          return svg
            .replace(/\swidth="\d+"/, ' width="20"')
            .replace(/\sheight="\d+"/, ' height="20"');
        }),
        illustration: FileSystemIconLoader('./assets/illustrations'),
        integrations: FileSystemIconLoader('./assets/integrations'),
        checkbox: FileSystemIconLoader('./assets/checkbox-tristate'),
      },
    }),

    // https://github.com/antfu/vite-plugin-inspect
    // Visit http://localhost:8080/__inspect/ to see the inspector
    Inspect(),

    // https://github.com/webfansplz/vite-plugin-vue-inspector
    Inspector({
      enabled: false,
      toggleComboKey: 'control-alt',
    }),
  ],

  // https://github.com/vitest-dev/vitest
  test: {
    include: ['test/**/*.test.ts'],
    environment: 'jsdom',
    deps: {
      inline: ['@vue', '@vueuse'],
    },
  },

  optimizeDeps: {
    include: [
      'nouislider',
      'wnumb',
      'trix',
    ],
  },
}));
