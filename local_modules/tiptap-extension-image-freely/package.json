{"name": "tiptap-extension-image-freely", "author": "coolswitch", "description": "Image extension for tiptap, support resize or rotation（tiptap的图片扩展，支持图片调整大小和旋转）", "version": "0.1.2", "homepage": "https://github.com/coolswitch/tiptap-extension-image-freely#readme", "keywords": ["tiptap", "tiptap extension", "tiptap extension resize", "tiptap extension rotate", "tiptap extension img", "tiptap extension image", "tiptap image resize", "tiptap image rotate"], "license": "MIT", "engines": {"node": ">=10"}, "main": "src/index.ts", "files": ["dist", "src"], "dependencies": {"@tiptap/core": "^2.0.3", "@tiptap/vue-3": "^2.0.3"}, "devDependencies": {"tslib": "^2.8.1", "typescript": "^5.9.3"}, "bugs": {"url": "https://github.com/coolswitch/tiptap-extension-image-freely/issues"}, "repository": "git+https://github.com/coolswitch/tiptap-extension-image-freely.git"}