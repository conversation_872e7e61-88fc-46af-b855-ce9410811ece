{"name": "tiptap-extension-image-upload", "author": "coolswitch", "description": "Image upload extension for tiptap, support image preview（tiptap的图片上传扩展，支持图片预览或占位）", "version": "0.1.6", "homepage": "https://github.com/coolswitch/tiptap-extension-image-upload#readme", "keywords": ["tiptap", "tiptap extension", "tiptap extension upload", "tiptap extension img", "tiptap extension image"], "license": "MIT", "engines": {"node": ">=10"}, "main": "src/index.ts", "files": ["dist", "src"], "dependencies": {"@tiptap/core": "^2.0.3", "@tiptap/vue-3": "^2.0.3", "prosemirror-model": "^1.19.2", "prosemirror-replaceattrs": "^1.0.0", "prosemirror-state": "^1.4.3", "prosemirror-view": "^1.31.5"}, "devDependencies": {"tslib": "^2.8.1", "typescript": "^5.9.3"}, "bugs": {"url": "https://github.com/coolswitch/tiptap-extension-image-upload/issues"}, "repository": "git+https://github.com/coolswitch/tiptap-extension-image-upload.git"}